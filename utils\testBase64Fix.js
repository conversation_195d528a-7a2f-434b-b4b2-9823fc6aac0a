// 测试修复后的 Base64 功能
import { base64Encode, base64Decode } from '@/utils/base64'

// 测试用例
const testCases = [
	'Hello World',
	'test123',
	'中文测试',
	'你好世界',
	'Hello World! 你好世界！',
	'!@#$%^&*()',
	'',
	'a',
	'1234567890',
	'The quick brown fox jumps over the lazy dog',
	'🌟🎉🚀', // emoji 测试
	'混合English中文123符号!@#'
]

// 运行测试
export function testBase64Fix() {
	console.log('=== 测试修复后的 Base64 功能 ===')
	
	let allPassed = true
	const results = []
	
	testCases.forEach((testCase, index) => {
		try {
			console.log(`\n测试 ${index + 1}: "${testCase}"`)
			
			const encoded = base64Encode(testCase)
			console.log('编码结果:', encoded)
			
			const decoded = base64Decode(encoded)
			console.log('解码结果:', decoded)
			
			const passed = testCase === decoded
			console.log('测试结果:', passed ? '✅ 通过' : '❌ 失败')
			
			if (!passed) {
				console.error('期望:', testCase)
				console.error('实际:', decoded)
				console.error('长度比较 - 期望:', testCase.length, '实际:', decoded.length)
				allPassed = false
			}
			
			results.push({
				input: testCase,
				encoded: encoded,
				decoded: decoded,
				passed: passed
			})
			
		} catch (error) {
			console.error(`测试 ${index + 1} 出错:`, error)
			allPassed = false
			results.push({
				input: testCase,
				error: error.message,
				passed: false
			})
		}
	})
	
	console.log('\n=== 测试总结 ===')
	console.log('总测试数:', testCases.length)
	console.log('通过数:', results.filter(r => r.passed).length)
	console.log('失败数:', results.filter(r => !r.passed).length)
	console.log('总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
	
	return {
		allPassed,
		results,
		summary: {
			total: testCases.length,
			passed: results.filter(r => r.passed).length,
			failed: results.filter(r => !r.passed).length
		}
	}
}

// 快速测试特定问题
export function quickTestUnicode() {
	console.log('\n=== 快速测试 Unicode 问题 ===')
	
	const problematicCase = 'Hello World! 你好世界！'
	
	try {
		console.log('原始字符串:', problematicCase)
		console.log('字符串长度:', problematicCase.length)
		
		const encoded = base64Encode(problematicCase)
		console.log('编码结果:', encoded)
		console.log('编码长度:', encoded.length)
		
		const decoded = base64Decode(encoded)
		console.log('解码结果:', decoded)
		console.log('解码长度:', decoded.length)
		
		const success = problematicCase === decoded
		console.log('测试结果:', success ? '✅ 成功' : '❌ 失败')
		
		if (!success) {
			console.log('字符对比:')
			for (let i = 0; i < Math.max(problematicCase.length, decoded.length); i++) {
				const orig = problematicCase.charAt(i) || '(无)'
				const dec = decoded.charAt(i) || '(无)'
				const match = orig === dec
				console.log(`位置 ${i}: "${orig}" vs "${dec}" ${match ? '✅' : '❌'}`)
			}
		}
		
		return success
		
	} catch (error) {
		console.error('快速测试失败:', error)
		return false
	}
}

// 在开发环境自动运行测试
if (process.env.NODE_ENV === 'development') {
	setTimeout(() => {
		try {
			quickTestUnicode()
			testBase64Fix()
		} catch (error) {
			console.error('Base64 修复测试失败:', error)
		}
	}, 1500)
}
