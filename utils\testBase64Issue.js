// 测试Base64加密解密问题
import { base64Encode, base64Decode } from './base64.js'
import { utils } from '@/api/index'

// 测试用例
const testCases = [
    'Hello World',
    'test123',
    '中文测试',
    'password123',
    '!@#$%^&*()',
    'Hello World! 你好世界！',
    '🌟🎉🚀',
    ''
]

// 测试Base64编码解码
export function testBase64() {
    console.log('=== 测试Base64编码解码 ===')
    
    let allPassed = true
    const results = []
    
    testCases.forEach((testCase, index) => {
        try {
            console.log(`\n测试 ${index + 1}: "${testCase}"`)
            
            // 直接测试base64函数
            const encoded = base64Encode(testCase)
            console.log('编码结果:', encoded)
            
            const decoded = base64Decode(encoded)
            console.log('解码结果:', decoded)
            
            const passed = testCase === decoded
            console.log('测试结果:', passed ? '✅ 通过' : '❌ 失败')
            
            if (!passed) {
                console.error('期望:', testCase)
                console.error('实际:', decoded)
                console.error('长度比较 - 期望:', testCase.length, '实际:', decoded.length)
                allPassed = false
            }
            
            results.push({
                input: testCase,
                encoded: encoded,
                decoded: decoded,
                passed: passed
            })
            
        } catch (error) {
            console.error(`测试 ${index + 1} 出错:`, error)
            allPassed = false
            results.push({
                input: testCase,
                error: error.message,
                passed: false
            })
        }
    })
    
    console.log('\n=== Base64测试总结 ===')
    console.log('总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
    
    return { allPassed, results }
}

// 测试utils加密解密
export function testUtilsEncrypt() {
    console.log('\n=== 测试utils加密解密 ===')
    
    let allPassed = true
    const results = []
    
    testCases.forEach((testCase, index) => {
        try {
            console.log(`\n加密测试 ${index + 1}: "${testCase}"`)
            
            const encrypted = utils.encrypt(testCase)
            console.log('加密结果:', encrypted)
            
            const decrypted = utils.decrypt(encrypted)
            console.log('解密结果:', decrypted)
            
            const passed = testCase === decrypted
            console.log('测试结果:', passed ? '✅ 通过' : '❌ 失败')
            
            if (!passed) {
                console.error('期望:', testCase)
                console.error('实际:', decrypted)
                allPassed = false
            }
            
            results.push({
                input: testCase,
                encrypted: encrypted,
                decrypted: decrypted,
                passed: passed
            })
            
        } catch (error) {
            console.error(`加密测试 ${index + 1} 出错:`, error)
            allPassed = false
            results.push({
                input: testCase,
                error: error.message,
                passed: false
            })
        }
    })
    
    console.log('\n=== 加密解密测试总结 ===')
    console.log('总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
    
    return { allPassed, results }
}

// 运行所有测试
export function runAllTests() {
    console.log('开始运行所有测试...\n')
    
    const base64Results = testBase64()
    const encryptResults = testUtilsEncrypt()
    
    console.log('\n=== 最终测试报告 ===')
    console.log('Base64测试:', base64Results.allPassed ? '✅ 通过' : '❌ 失败')
    console.log('加密解密测试:', encryptResults.allPassed ? '✅ 通过' : '❌ 失败')
    
    const overallSuccess = base64Results.allPassed && encryptResults.allPassed
    console.log('总体结果:', overallSuccess ? '✅ 全部功能正常' : '❌ 存在问题需要修复')
    
    return {
        base64: base64Results,
        encrypt: encryptResults,
        overall: overallSuccess
    }
}
