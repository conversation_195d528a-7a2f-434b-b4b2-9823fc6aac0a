<view class="container data-v-3120bf0f"><view class="header data-v-3120bf0f"><view class="header-title data-v-3120bf0f">小说下载器</view><view class="header-subtitle data-v-3120bf0f">激活码管理与每日签到</view></view><view class="nav-cards data-v-3120bf0f"><view data-event-opts="{{[['tap',[['goToActivation',['$event']]]]]}}" class="nav-card activation-card fade-in data-v-3120bf0f" bindtap="__e"><view class="nav-card-icon data-v-3120bf0f">🔑</view><view class="nav-card-title data-v-3120bf0f">激活码管理</view><view class="nav-card-desc data-v-3120bf0f">获取激活码、验证激活码、查询额度</view><view class="nav-card-arrow data-v-3120bf0f">→</view></view><view data-event-opts="{{[['tap',[['goToSignIn',['$event']]]]]}}" class="nav-card signin-card fade-in data-v-3120bf0f" style="animation-delay:0.1s;" bindtap="__e"><view class="nav-card-icon data-v-3120bf0f">📅</view><view class="nav-card-title data-v-3120bf0f">每日签到</view><view class="nav-card-desc data-v-3120bf0f">每日签到获取额度、查看签到历史</view><view class="nav-card-arrow data-v-3120bf0f">→</view></view></view><block wx:if="{{userInfo}}"><view class="quick-status fade-in data-v-3120bf0f" style="animation-delay:0.2s;"><view class="status-header data-v-3120bf0f"><view class="status-title data-v-3120bf0f">快速状态</view></view><view class="status-content data-v-3120bf0f"><view class="status-item data-v-3120bf0f"><view class="status-label data-v-3120bf0f">激活码状态:</view><view class="status-value success data-v-3120bf0f">已验证</view></view><view class="status-item data-v-3120bf0f"><view class="status-label data-v-3120bf0f">今日签到:</view><view class="{{['status-value','data-v-3120bf0f',hasSigned?'success':'warning']}}">{{''+(hasSigned?'已签到':'未签到')+''}}</view></view><view class="status-item data-v-3120bf0f"><view class="status-label data-v-3120bf0f">剩余额度:</view><view class="status-value data-v-3120bf0f">{{remainingTotalQuota||0}}</view></view></view></view></block><block wx:else><view class="no-user-tip fade-in data-v-3120bf0f" style="animation-delay:0.2s;"><view class="tip-icon data-v-3120bf0f">⚠️</view><view class="tip-text data-v-3120bf0f">您还未验证激活码，请先前往激活码管理页面验证激活码</view><button data-event-opts="{{[['tap',[['goToActivation',['$event']]]]]}}" class="btn btn-primary data-v-3120bf0f" bindtap="__e">立即验证</button></view></block></view>