<view class="test-container data-v-3e202046"><view class="test-header data-v-3e202046"><text class="test-title data-v-3e202046">加密解密测试</text></view><view class="test-content data-v-3e202046"><view class="test-section data-v-3e202046"><text class="section-title data-v-3e202046">Base64 编码测试</text><view class="test-item data-v-3e202046"><text class="data-v-3e202046">{{"原始文本: "+base64TestData}}</text></view><view class="test-item data-v-3e202046"><text class="data-v-3e202046">{{"编码结果: "+base64Encoded}}</text></view><view class="test-item data-v-3e202046"><text class="data-v-3e202046">{{"解码结果: "+base64Decoded}}</text></view><view class="test-item data-v-3e202046"><text class="{{['result','data-v-3e202046',base64Success?'success':'error']}}">{{'Base64 编码测试: '+(base64Success?'✅ 成功':'❌ 失败')+''}}</text></view></view><view class="test-section data-v-3e202046"><text class="section-title data-v-3e202046">加密解密测试</text><view class="test-item data-v-3e202046"><text class="data-v-3e202046">{{"原始密码: "+encryptTestData}}</text></view><view class="test-item data-v-3e202046"><text class="data-v-3e202046">{{"加密结果: "+encrypted}}</text></view><view class="test-item data-v-3e202046"><text class="data-v-3e202046">{{"解密结果: "+decrypted}}</text></view><view class="test-item data-v-3e202046"><text class="{{['result','data-v-3e202046',encryptSuccess?'success':'error']}}">{{'加密解密测试: '+(encryptSuccess?'✅ 成功':'❌ 失败')+''}}</text></view></view><view class="test-section data-v-3e202046"><text class="section-title data-v-3e202046">环境检测</text><view class="test-item data-v-3e202046"><text class="data-v-3e202046">{{"uni.base64Encode 可用: "+(hasUniBase64?'✅ 是':'❌ 否')}}</text></view><view class="test-item data-v-3e202046"><text class="data-v-3e202046">{{"运行环境: "+platform}}</text></view></view><button data-event-opts="{{[['tap',[['runTests',['$event']]]]]}}" class="test-btn data-v-3e202046" bindtap="__e">重新测试</button></view></view>