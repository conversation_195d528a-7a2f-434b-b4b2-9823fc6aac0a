
.test-container.data-v-3e202046 {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}
.test-header.data-v-3e202046 {
	text-align: center;
	padding: 40rpx 0;
}
.test-title.data-v-3e202046 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.test-content.data-v-3e202046 {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
}
.test-section.data-v-3e202046 {
	margin-bottom: 40rpx;
	padding-bottom: 30rpx;
	border-bottom: 1rpx solid #eee;
}
.test-section.data-v-3e202046:last-child {
	border-bottom: none;
	margin-bottom: 0;
}
.section-title.data-v-3e202046 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}
.test-item.data-v-3e202046 {
	margin-bottom: 15rpx;
	padding: 10rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
}
.test-item text.data-v-3e202046 {
	font-size: 28rpx;
	color: #666;
	word-break: break-all;
}
.result.data-v-3e202046 {
	font-weight: bold;
}
.result.success.data-v-3e202046 {
	color: #67c23a;
}
.result.error.data-v-3e202046 {
	color: #f56c6c;
}
.test-btn.data-v-3e202046 {
	width: 100%;
	height: 80rpx;
	background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
	color: #fff;
	border: none;
	border-radius: 40rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 30rpx;
}

