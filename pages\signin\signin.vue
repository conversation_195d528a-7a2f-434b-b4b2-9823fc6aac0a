<template>
	<view class="container">
		<!-- 页面头部 -->
		<view class="header">
			<view class="header-title">每日签到</view>
			<view class="header-subtitle">坚持签到，获得更多额度</view>
		</view>

		<!-- 签到卡片 -->
		<view class="card fade-in">
			<view class="card-header">
				<view class="card-title">每日签到</view>
				<view class="tag" :class="hasSigned ? 'tag-success' : 'tag-info'">
					{{ hasSigned ? '今日已签到' : '未签到' }}
				</view>
			</view>
			<view class="card-content">
				<!-- 签到信息 -->
				<view class="signin-info">
					<view class="signin-stats">
						<view class="stat-item">
							<view class="stat-number">{{ userStats.continuousDays || 0 }}</view>
							<view class="stat-label">连续签到天数</view>
						</view>
						<view class="stat-item">
							<view class="stat-number">{{ signInConfig.dailySignInReward || 5 }}</view>
							<view class="stat-label">每日奖励额度</view>
						</view>
					</view>

					<view class="signin-tip" v-if="signInConfig.enableContinuousReward">
						连续签到 {{ signInConfig.continuousDaysRequired || 7 }} 天可额外获得
						{{ signInConfig.continuousReward || 15 }} 额度
					</view>
				</view>

				<!-- 签到日历 -->
				<view class="calendar-container">
					<view class="calendar-header">
						<view class="calendar-title">{{ currentYear }}年{{ currentMonth }}月</view>
					</view>
					<view class="calendar-weekdays">
						<view class="weekday" v-for="day in weekDays" :key="day">{{ day }}</view>
					</view>
					<view class="calendar-dates">
						<view v-for="date in calendarDates" :key="date.day || Math.random()" class="calendar-date"
							:class="{
								'empty': !date.day,
								'signed': date.signed,
								'today': date.isToday,
								'disabled': date.disabled
							}">
							<text v-if="date.day">{{ date.day }}</text>
							<view v-if="date.signed" class="signed-mark">✓</view>
						</view>
					</view>
				</view>

				<!-- 签到按钮 -->
				<button class="btn signin-btn" :class="hasSigned || !userInfo ? 'btn-disabled' : 'btn-primary'"
					:disabled="hasSigned || !userInfo" @click="handleSignIn">
					{{ hasSigned ? '今日已签到' : '立即签到' }}
				</button>

				<!-- 签到历史 -->
				<view class="signin-history">
					<view class="history-header">
						<view class="history-title">签到历史</view>
						<view class="history-more" @click="loadMoreHistory" v-if="hasMoreHistory">
							查看更多
						</view>
					</view>
					<view class="history-list">
						<view v-for="item in signInHistory" :key="item.id" class="history-item"
							:class="item.extraReward ? 'extra-reward' : ''">
							<view class="history-content">
								<view class="history-reward">获得{{ item.rewardAmount }}额度</view>
								<view class="tag tag-success" v-if="item.extraReward">连续签到奖励</view>
							</view>
							<view class="history-time">{{ formatTime(item.signInTime) }}</view>
						</view>
						<view v-if="signInHistory.length === 0" class="no-data">
							暂无签到记录
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 提示卡片 -->
		<view v-if="!userInfo" class="card fade-in" style="animation-delay: 0.1s;">
			<view class="card-content">
				<view class="no-user-tip">
					<view class="tip-icon">🔑</view>
					<view class="tip-text">请先验证激活码后再进行签到</view>
					<button class="btn btn-primary" @click="goToActivation">前往激活码管理</button>
				</view>
			</view>
		</view>

		<!-- 导航按钮 -->
		<view class="nav-buttons">
			<button class="btn btn-primary nav-btn" @click="goToActivation">激活码管理</button>
			<button class="btn btn-secondary nav-btn" @click="goBack">返回首页</button>
		</view>
	</view>
</template>

<script>
	import {
		signInApi,
		tokenApi,
		utils
	} from '@/api/index'
	import { checkLogin } from '@/utils/auth'

	export default {
		data() {
			return {
				// 用户信息
				userInfo: null,

				// 签到相关
				hasSigned: false,
				signInConfig: {
					dailySignInReward: 5,
					enableContinuousReward: false,
					continuousDaysRequired: 7,
					continuousReward: 15
				},
				userStats: {
					continuousDays: 0
				},
				signInHistory: [],
				historyPage: 1,
				historyPageSize: 5,
				hasMoreHistory: false,

				// 日历相关
				currentDate: new Date(),
				weekDays: ['日', '一', '二', '三', '四', '五', '六'],
				calendarDates: [],
				signInDates: []
			}
		},

		computed: {
			currentYear() {
				return this.currentDate.getFullYear()
			},
			currentMonth() {
				return this.currentDate.getMonth() + 1
			}
		},

		onLoad() {
			// 检查登录状态
			if (!checkLogin()) {
				return
			}
			this.initPage()
		},

		methods: {
			// 初始化页面
			async initPage() {
				try {
					// 获取用户token状态
					const tokenRes = await tokenApi.getUserToken()
					if (tokenRes.code === 200 && tokenRes.data) {
						const tokenName = tokenRes.data.tokenName
						if (tokenName) {
							// 验证激活码获取用户信息
							const res = await tokenApi.getQuota(tokenName)
							if (res.code === 200) {
								this.userInfo = {
									tokenName: tokenName,
									tokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),
									tokenStatus: 0,
									tokenCompCode: '尚未绑定'
								}

								// 尝试获取额外的token信息
								try {
									const detailRes = await tokenApi.getTokenInfo(tokenName)
									if (detailRes.code === 200 && detailRes.rows && detailRes.rows.length > 0) {
										const tokenDetail = detailRes.rows[0]
										this.userInfo = {
											...this.userInfo,
											...tokenDetail
										}
									}
								} catch (error) {
									console.error('获取token详情失败:', error)
								}

								// 加载签到信息
								await this.loadUserSignInInfo()
							}
						}
					}
				} catch (error) {
					console.error('获取用户token失败:', error)
				}

				// 获取签到配置
				await this.loadSignInConfig()
				// 生成日历数据
				this.generateCalendar()
			},

			// 获取签到配置
			async loadSignInConfig() {
				try {
					const res = await signInApi.getSetting()
					if (res.code === 200) {
						this.signInConfig = res.data
					}
				} catch (error) {
					console.error('获取签到配置失败:', error)
				}
			},

			// 加载用户签到信息
			async loadUserSignInInfo() {
				if (!this.userInfo) return

				console.log('加载签到信息，用户信息:', this.userInfo)
				console.log('tokenName:', this.userInfo.tokenName)

				if (!this.userInfo.tokenName) {
					console.error('tokenName为空，无法加载签到信息')
					return
				}

				try {
					// 检查今日是否已签到
					const signRes = await signInApi.checkSignInToday(this.userInfo.tokenName)
					console.log('检查签到状态响应:', signRes)

					if (signRes.code === 200) {
						this.hasSigned = signRes.data
					}
				} catch (error) {
					console.error('检查签到状态失败:', error)
					this.hasSigned = false
				}

				// 加载签到历史
				await this.loadSignInHistory(true)
			},

			// 加载签到历史
			async loadSignInHistory(calculateContinuous = false) {
				if (!this.userInfo) return

				try {
					const pageSize = calculateContinuous ? 30 : this.historyPageSize
					const res = await signInApi.getHistory(
						this.userInfo.tokenName,
						this.historyPage,
						pageSize
					)

					if (res.code === 200) {
						if (!calculateContinuous) {
							this.signInHistory = this.historyPage === 1 ? res.rows : [...this.signInHistory, ...res
								.rows
							]
							this.hasMoreHistory = this.signInHistory.length < res.total
						} else {
							const historyList = res.rows || []

							// 生成当月签到记录
							this.generateMonthlyRecord(historyList)

							// 计算连续签到天数
							this.calculateContinuousDays(historyList)

							// 更新历史记录显示
							this.signInHistory = historyList.slice(0, this.historyPageSize)
							this.hasMoreHistory = historyList.length > this.historyPageSize
						}
					}
				} catch (error) {
					console.error('获取签到历史失败:', error)
				}
			},

			// 计算连续签到天数
			calculateContinuousDays(historyList) {
				if (!historyList || historyList.length === 0) {
					this.userStats.continuousDays = 0
					return
				}

				// 对签到历史按日期排序（最近的在前）
				historyList.sort((a, b) => new Date(b.signInTime) - new Date(a.signInTime))

				const today = new Date()
				today.setHours(0, 0, 0, 0)

				let continuousDays = 0
				let lastDate = null

				for (let i = 0; i < historyList.length; i++) {
					const signInDate = new Date(historyList[i].signInTime)
					signInDate.setHours(0, 0, 0, 0)

					if (i === 0) {
						const isToday = signInDate.getTime() === today.getTime()
						if (isToday) {
							continuousDays = 1
							lastDate = signInDate
						} else {
							const yesterday = new Date(today)
							yesterday.setDate(today.getDate() - 1)
							if (signInDate.getTime() === yesterday.getTime()) {
								continuousDays = 1
								lastDate = signInDate
							} else {
								break
							}
						}
					} else {
						const expectedDate = new Date(lastDate)
						expectedDate.setDate(expectedDate.getDate() - 1)

						if (signInDate.getTime() === expectedDate.getTime()) {
							continuousDays++
							lastDate = signInDate
						} else {
							break
						}
					}
				}

				this.userStats.continuousDays = continuousDays
			},

			// 生成当月签到记录
			generateMonthlyRecord(historyList) {
				if (!historyList || historyList.length === 0) {
					this.signInDates = []
					this.generateCalendar()
					return
				}

				const currentYear = this.currentYear
				const currentMonth = this.currentMonth - 1

				this.signInDates = historyList
					.map(item => new Date(item.signInTime))
					.filter(date => date.getFullYear() === currentYear && date.getMonth() === currentMonth)

				this.generateCalendar()
			},

			// 处理签到
			async handleSignIn() {
				if (!this.userInfo || this.hasSigned) return

				console.log('开始签到，用户信息:', this.userInfo)
				console.log('tokenName:', this.userInfo.tokenName)

				if (!this.userInfo.tokenName) {
					uni.showToast({
						title: '激活码信息异常，请重新进入页面',
						icon: 'none'
					})
					return
				}

				try {
					const res = await signInApi.doSignIn(this.userInfo.tokenName)
					console.log('签到API响应:', res)

					if (res.code === 200) {
						uni.showToast({
							title: `签到成功，获得${res.data.rewardAmount}额度`,
							icon: 'success',
							duration: 2000
						})

						this.hasSigned = true

						// 增加今天的日期到签到日期列表
						const today = new Date()
						this.signInDates.push(today)

						// 重新生成日历
						this.generateCalendar()

						// 更新连续签到天数
						this.userStats.continuousDays += 1

						// 更新签到历史
						setTimeout(() => {
							this.historyPage = 1
							this.loadSignInHistory()
						}, 500)
					} else {
						uni.showToast({
							title: res.msg || '签到失败',
							icon: 'none',
							duration: 2000
						})
					}
				} catch (error) {
					console.error('签到失败:', error)
					uni.showToast({
						title: '签到失败，请稍后再试',
						icon: 'none',
						duration: 2000
					})
				}
			},

			// 加载更多历史
			loadMoreHistory() {
				this.historyPage++
				this.loadSignInHistory()
			},

			// 生成日历数据
			generateCalendar() {
				const year = this.currentYear
				const month = this.currentMonth - 1

				const firstDay = new Date(year, month, 1).getDay()
				const daysInMonth = new Date(year, month + 1, 0).getDate()

				const today = new Date()
				const isCurrentMonth = today.getFullYear() === year && today.getMonth() === month

				let dates = []

				// 填充前面的空白
				for (let i = 0; i < firstDay; i++) {
					dates.push({
						day: null
					})
				}

				// 填充日期
				for (let i = 1; i <= daysInMonth; i++) {
					const dateObj = new Date(year, month, i)
					const isToday = isCurrentMonth && today.getDate() === i
					const signed = this.signInDates.some(date =>
						date.getFullYear() === year &&
						date.getMonth() === month &&
						date.getDate() === i
					)

					const disabled = dateObj > today

					dates.push({
						day: i,
						isToday,
						signed,
						disabled
					})
				}

				this.calendarDates = dates
			},

			// 格式化时间
			formatTime(time) {
				return utils.formatDate(time, 'MM-DD HH:mm')
			},

			// 前往激活码页面
			goToActivation() {
				uni.navigateTo({
					url: '/pages/activation/activation'
				})
			},

			// 返回首页
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style scoped>
	/* 页面容器 */
	.container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 20rpx;
	}

	/* 页面头部 */
	.header {
		text-align: center;
		padding: 40rpx 0;
		color: #ffffff;
	}

	.header-title {
		font-size: 48rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	}

	.header-subtitle {
		font-size: 28rpx;
		opacity: 0.9;
	}

	/* 卡片样式 */
	.card {
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 30rpx;
		overflow: hidden;
		transition: all 0.3s ease;
	}

	.card:hover {
		transform: translateY(-4rpx);
		box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
	}

	.fade-in {
		animation: fadeInUp 0.6s ease-out;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(30rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
	}

	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.card-content {
		padding: 30rpx;
	}

	/* 标签样式 */
	.tag {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		font-weight: bold;
	}

	.tag-success {
		background-color: #f0f9eb;
		color: #67C23A;
		border: 1rpx solid #c2e7b0;
	}

	.tag-info {
		background-color: #f4f4f5;
		color: #909399;
		border: 1rpx solid #d3d4d6;
	}

	/* 按钮样式 */
	.btn {
		padding: 20rpx 40rpx;
		border-radius: 25rpx;
		border: none;
		font-size: 28rpx;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.3s ease;
		text-align: center;
		display: inline-block;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
	}

	.btn-primary {
		background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
		color: #ffffff;
	}

	.btn-secondary {
		background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
		color: #ffffff;
	}

	.btn-disabled {
		background-color: #c0c4cc;
		color: #ffffff;
		cursor: not-allowed;
		opacity: 0.6;
	}

	.btn-disabled:hover {
		transform: none;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	/* 签到信息 */
	.signin-info {
		margin-bottom: 30rpx;
	}

	.signin-stats {
		display: flex;
		justify-content: space-around;
		margin-bottom: 20rpx;
	}

	.stat-item {
		text-align: center;
		flex: 1;
	}

	.stat-number {
		font-size: 48rpx;
		font-weight: bold;
		color: #409EFF;
		margin-bottom: 10rpx;
	}

	.stat-label {
		font-size: 24rpx;
		color: #666666;
	}

	.signin-tip {
		text-align: center;
		font-size: 26rpx;
		color: #E6A23C;
		background-color: #fdf6ec;
		padding: 15rpx;
		border-radius: 8rpx;
		border-left: 4rpx solid #E6A23C;
	}

	/* 日历样式 */
	.calendar-container {
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
	}

	.calendar-header {
		text-align: center;
		margin-bottom: 20rpx;
	}

	.calendar-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.calendar-weekdays {
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		gap: 10rpx;
		margin-bottom: 15rpx;
		padding-bottom: 15rpx;
		border-bottom: 1rpx solid #e0e0e0;
	}

	.weekday {
		text-align: center;
		font-size: 24rpx;
		font-weight: bold;
		color: #666666;
		padding: 10rpx 0;
	}

	.calendar-dates {
		display: grid;
		grid-template-columns: repeat(7, 1fr);
		gap: 10rpx;
	}

	.calendar-date {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		position: relative;
		margin: 0 auto;
		font-size: 26rpx;
		transition: all 0.3s ease;
	}

	.calendar-date.empty {
		visibility: hidden;
	}

	.calendar-date.signed {
		background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(103, 194, 58, 0.3);
	}

	.calendar-date.today {
		border: 3rpx solid #409EFF;
		font-weight: bold;
		color: #409EFF;
	}

	.calendar-date.disabled {
		color: #c0c4cc;
	}

	.signed-mark {
		position: absolute;
		font-size: 20rpx;
		font-weight: bold;
	}

	/* 签到按钮 */
	.signin-btn {
		width: 100%;
		height: 80rpx;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		box-shadow: 0 6rpx 20rpx rgba(64, 158, 255, 0.3);
	}

	/* 签到历史 */
	.signin-history {
		margin-top: 20rpx;
	}

	.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.history-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}

	.history-more {
		font-size: 24rpx;
		color: #409EFF;
		padding: 10rpx;
	}

	.history-list {
		max-height: 400rpx;
		overflow-y: auto;
	}

	.history-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		background-color: #f8f9fa;
		border-radius: 8rpx;
		margin-bottom: 15rpx;
		border-left: 4rpx solid #409EFF;
	}

	.history-item.extra-reward {
		border-left-color: #67C23A;
		background: linear-gradient(90deg, #f0f9eb 0%, #f8f9fa 100%);
	}

	.history-content {
		display: flex;
		align-items: center;
		gap: 15rpx;
	}

	.history-reward {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}

	.history-time {
		font-size: 24rpx;
		color: #999999;
	}

	.no-data {
		text-align: center;
		color: #999999;
		padding: 60rpx 0;
		font-size: 28rpx;
	}

	/* 无用户提示 */
	.no-user-tip {
		text-align: center;
		padding: 40rpx 20rpx;
	}

	.tip-icon {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}

	.tip-text {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 30rpx;
		line-height: 1.5;
	}

	/* 导航按钮 */
	.nav-buttons {
		display: flex;
		gap: 20rpx;
		margin-top: 30rpx;
	}

	.nav-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 40rpx;
		font-size: 30rpx;
	}

	/* 响应式设计 */
	@media screen and (max-width: 750rpx) {
		.signin-stats {
			flex-direction: column;
			gap: 20rpx;
		}
	}

	@media screen and (max-width: 600rpx) {
		.calendar-dates {
			gap: 5rpx;
		}

		.calendar-date {
			width: 50rpx;
			height: 50rpx;
			font-size: 24rpx;
		}
	}
</style>
