// 测试Mock API移除后的状态
import { loginApi, getInfo, logout, getCodeImg, phoneLogin } from '@/api/user'
import { signInApi, tokenApi } from '@/api/index'

// 测试API函数是否正确导入
export function testApiImports() {
    console.log('=== 测试API导入 ===')
    
    const tests = [
        { name: 'loginApi', func: loginApi },
        { name: 'getInfo', func: getInfo },
        { name: 'logout', func: logout },
        { name: 'getCodeImg', func: getCodeImg },
        { name: 'phoneLogin', func: phoneLogin },
        { name: 'signInApi.getSetting', func: signInApi.getSetting },
        { name: 'tokenApi.getQuota', func: tokenApi.getQuota }
    ]
    
    let allPassed = true
    
    tests.forEach(test => {
        const isFunction = typeof test.func === 'function'
        console.log(`${test.name}: ${isFunction ? '✅ 正常' : '❌ 异常'}`)
        if (!isFunction) {
            allPassed = false
        }
    })
    
    console.log('API导入测试:', allPassed ? '✅ 全部通过' : '❌ 存在问题')
    return allPassed
}

// 测试是否还有mock API的引用
export function testMockApiRemoval() {
    console.log('\n=== 测试Mock API移除 ===')
    
    let hasMockReferences = false
    
    // 检查是否还能访问mockApi
    try {
        // 尝试导入mockApi，如果成功说明还有引用
        const mockApi = require('@/utils/mockApi')
        if (mockApi) {
            console.log('❌ 仍然可以访问mockApi')
            hasMockReferences = true
        }
    } catch (error) {
        console.log('✅ mockApi已成功移除')
    }
    
    // 检查shouldUseMockApi函数
    try {
        const { shouldUseMockApi } = require('@/utils/mockApi')
        if (shouldUseMockApi) {
            console.log('❌ shouldUseMockApi函数仍然存在')
            hasMockReferences = true
        }
    } catch (error) {
        console.log('✅ shouldUseMockApi函数已移除')
    }
    
    const passed = !hasMockReferences
    console.log('Mock API移除测试:', passed ? '✅ 完全移除' : '❌ 仍有残留')
    return passed
}

// 测试API调用（会失败，但这是预期的）
export async function testApiCalls() {
    console.log('\n=== 测试API调用 ===')
    console.log('注意：由于没有真实的API服务器，以下调用会失败，这是正常的')
    
    const tests = [
        {
            name: '获取验证码',
            call: () => getCodeImg()
        },
        {
            name: '获取签到配置',
            call: () => signInApi.getSetting()
        }
    ]
    
    for (const test of tests) {
        try {
            console.log(`\n测试: ${test.name}`)
            const result = await test.call()
            console.log(`${test.name} - 意外成功:`, result)
        } catch (error) {
            console.log(`${test.name} - 预期失败:`, error.message)
            
            // 检查错误信息是否符合预期
            if (error.message.includes('请先配置正确的 API 地址') || 
                error.message.includes('网络') || 
                error.message.includes('连接')) {
                console.log(`${test.name} - ✅ 错误信息正确`)
            } else {
                console.log(`${test.name} - ⚠️ 错误信息异常`)
            }
        }
    }
    
    console.log('\nAPI调用测试完成（失败是预期的）')
    return true
}

// 运行所有测试
export async function runAllRemovalTests() {
    console.log('开始测试Mock API移除效果...\n')
    
    const importTest = testApiImports()
    const removalTest = testMockApiRemoval()
    await testApiCalls()
    
    console.log('\n=== 测试结果汇总 ===')
    console.log('API导入测试:', importTest ? '✅ 通过' : '❌ 失败')
    console.log('Mock API移除测试:', removalTest ? '✅ 通过' : '❌ 失败')
    
    const overallSuccess = importTest && removalTest
    console.log('总体结果:', overallSuccess ? '✅ Mock API已成功移除' : '❌ 移除不完整')
    
    if (overallSuccess) {
        console.log('\n🎉 恭喜！Mock API已完全移除！')
        console.log('现在应用将直接调用真实的API接口。')
        console.log('请确保：')
        console.log('1. 在 config/index.js 中配置正确的API地址')
        console.log('2. 确保API服务器正在运行')
        console.log('3. 检查API接口的数据格式是否与前端匹配')
    } else {
        console.log('\n⚠️ Mock API移除不完整，请检查：')
        if (!importTest) {
            console.log('- API函数导入存在问题')
        }
        if (!removalTest) {
            console.log('- 仍有Mock API的引用残留')
        }
    }
    
    return {
        importTest,
        removalTest,
        overall: overallSuccess
    }
}

// 清理函数
export function cleanup() {
    console.log('清理测试数据...')
    // 这里可以添加清理逻辑
    console.log('清理完成')
}
