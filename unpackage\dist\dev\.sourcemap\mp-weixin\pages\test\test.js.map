{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/test/test.vue?6ce9", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/test/test.vue?a7d6", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/test/test.vue?bb72", "uni-app:///pages/test/test.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/test/test.vue?f989", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/test/test.vue?9913"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "base64TestData", "base64Encoded", "base64Decoded", "base64Success", "encryptTestData", "encrypted", "decrypted", "encryptSuccess", "hasUniBase64", "platform", "onLoad", "methods", "runTests", "console", "uni", "title", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACqC;;;AAGxF;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsrB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2D1sB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACAC;;MAEA;MACA;;MAEA;MACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACA;MACA;;MAEA;MACA;MACAC;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAAmgC,CAAgB,69BAAG,EAAC,C;;;;;;;;;;;ACAvhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/test/test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/test/test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./test.vue?vue&type=template&id=3e202046&scoped=true&\"\nvar renderjs\nimport script from \"./test.vue?vue&type=script&lang=js&\"\nexport * from \"./test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./test.vue?vue&type=style&index=0&id=3e202046&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e202046\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/test/test.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test.vue?vue&type=template&id=3e202046&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"test-container\">\n\t\t<view class=\"test-header\">\n\t\t\t<text class=\"test-title\">加密解密测试</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"test-content\">\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<text class=\"section-title\">Base64 编码测试</text>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text>原始文本: {{ base64TestData }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text>编码结果: {{ base64Encoded }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text>解码结果: {{ base64Decoded }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text class=\"result\" :class=\"base64Success ? 'success' : 'error'\">\n\t\t\t\t\t\tBase64 编码测试: {{ base64Success ? '✅ 成功' : '❌ 失败' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<text class=\"section-title\">加密解密测试</text>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text>原始密码: {{ encryptTestData }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text>加密结果: {{ encrypted }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text>解密结果: {{ decrypted }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text class=\"result\" :class=\"encryptSuccess ? 'success' : 'error'\">\n\t\t\t\t\t\t加密解密测试: {{ encryptSuccess ? '✅ 成功' : '❌ 失败' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<text class=\"section-title\">环境检测</text>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text>uni.base64Encode 可用: {{ hasUniBase64 ? '✅ 是' : '❌ 否' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"test-item\">\n\t\t\t\t\t<text>运行环境: {{ platform }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<button class=\"test-btn\" @click=\"runTests\">重新测试</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { utils } from '@/api/index'\n\t\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tbase64TestData: 'Hello World! 你好世界！',\n\t\t\t\tbase64Encoded: '',\n\t\t\t\tbase64Decoded: '',\n\t\t\t\tbase64Success: false,\n\t\t\t\t\n\t\t\t\tencryptTestData: 'test_password_123',\n\t\t\t\tencrypted: '',\n\t\t\t\tdecrypted: '',\n\t\t\t\tencryptSuccess: false,\n\t\t\t\t\n\t\t\t\thasUniBase64: false,\n\t\t\t\tplatform: ''\n\t\t\t}\n\t\t},\n\t\t\n\t\tonLoad() {\n\t\t\tthis.runTests()\n\t\t},\n\t\t\n\t\tmethods: {\n\t\t\trunTests() {\n\t\t\t\tconsole.log('开始运行测试...')\n\n\t\t\t\t// 检测环境\n\t\t\t\tthis.hasUniBase64 = typeof uni.base64Encode === 'function'\n\n\t\t\t\t// 使用新的 API 获取平台信息\n\t\t\t\ttry {\n\t\t\t\t\tif (typeof uni.getDeviceInfo === 'function') {\n\t\t\t\t\t\tthis.platform = uni.getDeviceInfo().platform\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 降级到旧 API\n\t\t\t\t\t\tthis.platform = uni.getSystemInfoSync().platform\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tthis.platform = 'unknown'\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 测试 Base64 编码\n\t\t\t\ttry {\n\t\t\t\t\tthis.base64Encoded = utils.base64Encode(this.base64TestData)\n\t\t\t\t\tthis.base64Decoded = utils.base64Decode(this.base64Encoded)\n\t\t\t\t\tthis.base64Success = this.base64TestData === this.base64Decoded\n\t\t\t\t\tconsole.log('Base64 编码测试:', this.base64Success ? '成功' : '失败')\n\t\t\t\t\tconsole.log('原始数据:', this.base64TestData)\n\t\t\t\t\tconsole.log('编码结果:', this.base64Encoded)\n\t\t\t\t\tconsole.log('解码结果:', this.base64Decoded)\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Base64 编码测试失败:', error)\n\t\t\t\t\tthis.base64Success = false\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 测试加密解密\n\t\t\t\ttry {\n\t\t\t\t\tthis.encrypted = utils.encrypt(this.encryptTestData)\n\t\t\t\t\tthis.decrypted = utils.decrypt(this.encrypted)\n\t\t\t\t\tthis.encryptSuccess = this.encryptTestData === this.decrypted\n\t\t\t\t\tconsole.log('加密解密测试:', this.encryptSuccess ? '成功' : '失败')\n\t\t\t\t\tconsole.log('原始数据:', this.encryptTestData)\n\t\t\t\t\tconsole.log('加密结果:', this.encrypted)\n\t\t\t\t\tconsole.log('解密结果:', this.decrypted)\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加密解密测试失败:', error)\n\t\t\t\t\tthis.encryptSuccess = false\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示结果\n\t\t\t\tconst overallSuccess = this.base64Success && this.encryptSuccess\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: overallSuccess ? '所有测试通过' : '部分测试失败',\n\t\t\t\t\ticon: overallSuccess ? 'success' : 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.test-container {\n\t\tpadding: 20rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.test-header {\n\t\ttext-align: center;\n\t\tpadding: 40rpx 0;\n\t}\n\t\n\t.test-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.test-content {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t}\n\t\n\t.test-section {\n\t\tmargin-bottom: 40rpx;\n\t\tpadding-bottom: 30rpx;\n\t\tborder-bottom: 1rpx solid #eee;\n\t}\n\t\n\t.test-section:last-child {\n\t\tborder-bottom: none;\n\t\tmargin-bottom: 0;\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: block;\n\t}\n\t\n\t.test-item {\n\t\tmargin-bottom: 15rpx;\n\t\tpadding: 10rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 8rpx;\n\t}\n\t\n\t.test-item text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tword-break: break-all;\n\t}\n\t\n\t.result {\n\t\tfont-weight: bold;\n\t}\n\t\n\t.result.success {\n\t\tcolor: #67c23a;\n\t}\n\t\n\t.result.error {\n\t\tcolor: #f56c6c;\n\t}\n\t\n\t.test-btn {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 40rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tmargin-top: 30rpx;\n\t}\n</style>\n", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test.vue?vue&type=style&index=0&id=3e202046&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./test.vue?vue&type=style&index=0&id=3e202046&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755074429682\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}