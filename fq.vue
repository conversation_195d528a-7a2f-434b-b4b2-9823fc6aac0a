<template>
	<div class="user-signin-container">
		<el-page-header @back="goBack" :content="this.$route.meta.title" style="margin: 10px 10px;"></el-page-header>
		<el-row :gutter="20">
			<!-- 左侧签到部分 -->
			<el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
				<el-card class="signin-card">
					<div class="card-header" slot="header">
						<span class="card-title">每日签到</span>
						<el-tag v-if="hasSigned" type="success">今日已签到</el-tag>
						<el-tag v-else type="info">未签到</el-tag>
					</div>

					<div class="signin-content">
						<div class="signin-info">
							<p class="signin-status">
								您已连续签到 <span class="highlight">{{ userStats.continuousDays || 0 }}</span> 天
							</p>
							<p class="signin-tip">
								每日签到可获得 <span class="highlight">{{ signInConfig.dailySignInReward || 5 }}</span> 额度
							</p>
							<p class="signin-tip" v-if="signInConfig.enableContinuousReward">
								连续签到 {{ signInConfig.continuousDaysRequired || 7 }} 天可额外获得 {{ signInConfig.continuousReward || 15 }} 额度
							</p>
						</div>

						<div class="calendar-container">
							<div class="calendar-title">{{ currentYear }}年{{ currentMonth }}月</div>
							<div class="calendar-week">
								<div class="week-day" v-for="day in weekDays" :key="day">{{ day }}</div>
							</div>
							<div class="calendar-dates">
								<div
										v-for="date in calendarDates"
										:key="date.day"
										class="calendar-date"
										:class="{
                    'empty': !date.day,
                    'signed': date.signed,
                    'today': date.isToday,
                    'disabled': date.disabled
                  }"
								>
									{{ date.day }}
									<div v-if="date.signed" class="signed-mark">✓</div>
								</div>
							</div>
						</div>

						<el-button
								type="primary"
								:disabled="hasSigned || !userInfo"
								@click="handleSignIn"
								class="signin-btn">
							{{ hasSigned ? '今日已签到' : '立即签到' }}
						</el-button>

						<div class="signin-history">
							<div class="history-title">
								<span>签到历史</span>
								<el-button type="text" @click="loadMoreHistory" v-if="hasMoreHistory">查看更多</el-button>
							</div>
							<el-scrollbar style="height: 200px">
								<el-timeline>
									<el-timeline-item
											v-for="item in signInHistory"
											:key="item.id"
											:timestamp="item.signInTime"
											placement="top"
											:type="item.extraReward ? 'success' : 'primary'"
									>
										<p>获得{{ item.rewardAmount }}额度
											<el-tag v-if="item.extraReward" size="mini" type="success">连续签到奖励</el-tag>
										</p>
									</el-timeline-item>
								</el-timeline>
								<div v-if="signInHistory.length === 0" class="no-data">暂无签到记录</div>
							</el-scrollbar>
						</div>
					</div>
				</el-card>
			</el-col>

			<!-- 右侧激活码部分 -->
			<el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
				<!-- 操作指南卡片 -->
				<el-card class="guide-card" v-if="!userInfo" shadow="hover">
					<div class="card-header" slot="header">
						<span class="card-title"><i class="el-icon-guide"></i> 激活码使用指南</span>
					</div>
					<div class="guide-content">
						<el-steps direction="vertical" :active="1" class="custom-steps">
							<el-step title="获取激活码" description="点击下方的获取激活码按钮">
								<template slot="icon">
									<i class="el-icon-key"></i>
								</template>
							</el-step>
							<el-step title="去小说下载器中激活" description="复制激活码，前往小说下载器中进行激活">
								<template slot="icon">
									<i class="el-icon-link"></i>
								</template>
							</el-step>
							<el-step title="验证激活码" description="返回此页面，输入激活码进行验证">
								<template slot="icon">
									<i class="el-icon-check"></i>
								</template>
							</el-step>
						</el-steps>

						<div class="guide-tips">
							<el-alert
									title="提示：激活码仅限本人使用，请勿分享给他人"
									type="info"
									show-icon
									:closable="false">
							</el-alert>
						</div>
					</div>
				</el-card>

				<el-card class="token-card">
					<div class="card-header" slot="header">
						<span class="card-title">我的激活码</span>
					</div>

					<div v-if="!userInfo" class="token-content">
						<div class="no-token">
							<i class="el-icon-key"></i>
							<p>请输入您的激活码</p>
							<el-input
									v-model="inputToken"
									placeholder="请输入激活码"
									class="token-input"
							></el-input>
							<el-button type="primary" @click="verifyToken">验证激活码</el-button>
						</div>
					</div>

					<div v-else class="token-content">
						<el-alert
								title="请妥善保管您的激活码，请勿泄露给他人"
								type="warning"
								show-icon
								:closable="false"
						></el-alert>

						<div class="token-info">
							<div class="info-item">
								<span class="label">激活码:</span>
								<div class="value-with-copy">
									<span class="value">{{ userInfo.tokenName }}</span>
									<el-button type="text" icon="el-icon-copy-document" @click="copyText(userInfo.tokenName)"></el-button>
								</div>
							</div>
							<div class="info-item">
								<span class="label">绑定设备:</span>
								<span class="value">{{ userInfo.tokenCompCode || '未绑定设备' }}</span>
							</div>
							<div class="info-item">
								<span class="label">创建时间:</span>
								<span class="value">{{ userInfo.tokenTime }}</span>
							</div>
							<div class="info-item">
								<span class="label">账户状态:</span>
								<el-tag size="small" :type="userInfo.tokenStatus === 0 ? 'success' : 'danger'">
									{{ userInfo.tokenStatus === 0 ? '正常' : '已禁用' }}
								</el-tag>
							</div>
						</div>

						<el-divider content-position="center">额度使用情况</el-divider>

						<div class="quota-info">
							<!-- 今日使用额度和剩余总额度展示 -->
							<el-row :gutter="20" class="main-quota-display">
								<el-col :span="12">
									<div class="quota-card today-usage">
										<div class="quota-icon">
											<i class="el-icon-date"></i>
										</div>
										<div class="quota-content">
											<div class="quota-value">{{ todayUsage || 0 }}</div>
											<div class="quota-label">今日使用额度</div>
										</div>
									</div>
								</el-col>
								<el-col :span="12">
									<div class="quota-card remaining-total">
										<div class="quota-icon">
											<i class="el-icon-wallet"></i>
										</div>
										<div class="quota-content">
											<div class="quota-value">{{ remainingTotalQuota || 0 }}</div>
											<div class="quota-label">剩余总额度</div>
											<div class="quota-sublabel">基础额度 + 签到额度 + 活动额度 + 永久额度</div>
										</div>
									</div>
								</el-col>
							</el-row>

							<!-- 详细额度信息 -->
							<el-divider content-position="center">详细额度信息</el-divider>
							<el-row :gutter="15" class="detailed-quota">
								<el-col :span="6">
									<div class="quota-item">
										<div class="quota-value">{{ userQuota.baseLimit || 0 }}</div>
										<div class="quota-label">基础额度</div>
									</div>
								</el-col>
								<el-col :span="6">
									<div class="quota-item">
										<div class="quota-value">{{ userQuota.activityLimit || 0 }}</div>
										<div class="quota-label">活动额度</div>
									</div>
								</el-col>
								<el-col :span="6">
									<div class="quota-item">
										<div class="quota-value">{{ userQuota.tempLimit || 0 }}</div>
										<div class="quota-label">签到额度</div>
									</div>
								</el-col>
								<el-col :span="6">
									<div class="quota-item">
										<div class="quota-value">{{ userQuota.permanentQuota || 0 }}</div>
										<div class="quota-label">永久额度</div>
									</div>
								</el-col>
							</el-row>

							<div class="info-item" style="margin-top: 15px;">
								<el-alert
										title="注意：永久额度是一次性消耗品，不会刷新重置！永久额度只有在其他额度用完后才会使用！"
										type="warning"
										show-icon
										:closable="false"
								></el-alert>
							</div>
						</div>

						<div class="token-actions">
							<el-button type="success" @click="refreshQuota">刷新额度</el-button>
							<el-button type="danger" @click="logout">重置激活码</el-button>
							<el-button type="warning" icon="el-icon-star-off" @click="openDonateDialog">赞助我们</el-button>
						</div>
					</div>
				</el-card>
				<el-card class="token-card" v-if="!addTokenFlag">
					<div class="no-token">
						<el-input
								v-model="newToken"
								placeholder="点击按钮获取激活码"
								class="token-input"
						></el-input>
						<el-button type="primary" @click="addToken">获取激活码</el-button>
					</div>
				</el-card>
			</el-col>
		</el-row>

		<!-- 激活码弹窗 -->
		<el-dialog
				title="激活码获取成功"
				:visible.sync="tokenDialogVisible"
				width="50%"
				@closed="handleTokenDialogClosed">
			<div class="token-dialog-content">
				<p class="token-dialog-tip"><i class="el-icon-warning"></i> 请妥善保管您的激活码，请勿泄露给他人</p>
				<div class="token-dialog-code">
					<span class="token-dialog-label">您的激活码:</span>
					<div class="token-dialog-value-copy">
						<span class="token-dialog-value">{{ newToken }}</span>
						<el-button type="text" icon="el-icon-copy-document" @click="copyText(newToken)">复制</el-button>
					</div>
				</div>

				<!-- 优化激活码使用步骤提示 -->
				<div class="token-dialog-steps">
					<p class="token-dialog-steps-title"><i class="el-icon-info"></i> 使用步骤</p>
					<div class="token-dialog-steps-content">
						<div class="step-item">
							<div class="step-number">1</div>
							<div class="step-text">已获取激活码</div>
							<div class="step-status completed"><i class="el-icon-success"></i></div>
						</div>
						<div class="step-item">
							<div class="step-number">2</div>
							<div class="step-text">去小说下载器中激活该激活码</div>
							<div class="step-status"><i class="el-icon-arrow-right"></i></div>
						</div>
						<div class="step-item">
							<div class="step-number">3</div>
							<div class="step-text">回到此页面，验证激活码</div>
							<div class="step-status"><i class="el-icon-arrow-right"></i></div>
						</div>
					</div>
				</div>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="tokenDialogVisible = false">确定</el-button>
			</span>
		</el-dialog>

		<!-- 赞助弹窗 -->
		<el-dialog
				title="支持我们的开发"
				:visible.sync="donateDialogVisible"
				width="60%">
			<div class="donate-dialog-content">
				<div class="donate-header">
					<i class="el-icon-star-on"></i>
					<h3>感谢您的支持</h3>
					<i class="el-icon-star-on"></i>
				</div>

				<div class="donate-description">
					<p>亲爱的用户，感谢您使用我们的小说下载器！</p>
					<p>您的支持是我们持续改进的动力。如果您觉得我们的工具对您有所帮助，可以考虑给予我们一些赞助，这将帮助我们：</p>
					<ul>
						<li><i class="el-icon-check"></i> 持续开发更多功能</li>
						<li><i class="el-icon-check"></i> 提供更稳定的服务器</li>
						<li><i class="el-icon-check"></i> 维持下载器的稳定</li>
					</ul>
					<p class="donate-tip">您的每一份赞助，我们都将心存感激！</p>
				</div>

				<div class="donate-methods">
					<div class="donate-method-item">
						<div class="donate-method-title">
							<i class="el-icon-money"></i>
							<span>支付宝</span>
						</div>
						<div class="donate-qr-code alipay-bg">
							<!-- 这里放支付宝收款码图片 -->
							<div class="qr-placeholder">
								<el-image :src="zfbImg" :preview-src-list="[zfbImg]"></el-image>
							</div>
						</div>
					</div>
					<div class="donate-method-item">
						<div class="donate-method-title">
							<i class="el-icon-chat-dot-square"></i>
							<span>微信支付</span>
						</div>
						<div class="donate-qr-code wechat-bg">
							<!-- 这里放微信收款码图片 -->
							<div class="qr-placeholder">
								<el-image :src="wechatImg" :preview-src-list="[wechatImg]"></el-image>
							</div>
						</div>
					</div>
				</div>

				<div class="donate-footer">
					<p><i class="el-icon-warning-outline"></i> 赞助为自愿行为，不影响软件的正常使用</p>
					<p>赞助后请留下您的联系方式或者激活码，我们将回馈您一定额度支持</p>
				</div>
			</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="donateDialogVisible = false">关闭</el-button>
				<el-button type="success" @click="donateDialogVisible = false">已完成赞助</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import CryptoJS from 'crypto-js'
import {
	addFqToken,
	checkSignInToday,
	doSignIn,
	getCodeInfo,
	getExtraTokenInfo,
	getSetting,
	getSettingHistory, getUserFqToken, refQuota
} from "@/api";

import wechatImg from "@/assets/images/localTool/fq_token/wechat.jpg"
import zfbImg from "@/assets/images/localTool/fq_token/zfb.jpg"

export default {
	name: "UserSignInView",
	data() {
		return {
			// 用户信息
			userInfo: null,
			inputToken: '',
			userQuota: {
				baseLimit: 0,
				activityLimit: 0,
				totalLimit: 0,
				currentUseNum: 0,
				remainingLimit: 0,
				permanentQuota: 0,
				todayUsed: 0  // 今日使用额度
			},
			// 签到相关
			hasSigned: false,
			signInConfig: {
				dailySignInReward: 5,
				enableContinuousReward: false,
				continuousDaysRequired: 7,
				continuousReward: 15
			},
			userStats: {
				continuousDays: 0
			},
			signInHistory: [],
			historyPage: 1,
			historyPageSize: 5,
			hasMoreHistory: false,
			// 日历相关
			currentDate: new Date(),
			weekDays: ['日', '一', '二', '三', '四', '五', '六'],
			calendarDates: [],
			signInDates: [], // 已签到日期的数组
			newToken: '',
			addTokenFlag: true, // 是否添加token
			tokenDialogVisible: false, // 激活码弹窗显示状态
			donateDialogVisible: false, // 赞助弹窗显示状态
			zfbImg,
			wechatImg,
			tokenName: ''
		}
	},
	computed: {
		currentYear() {
			return this.currentDate.getFullYear()
		},
		currentMonth() {
			return this.currentDate.getMonth() + 1
		},
		// 今日使用额度（这里可以根据实际需求调整计算逻辑）
		todayUsage() {
			// 假设从userQuota中获取今日使用量，如果没有相关字段，可以设为0或从其他地方获取
			return this.userQuota.currentUseNum || 0
		},
		// 剩余总额度 = 总额度 + 永久额度 - 已使用
		remainingTotalQuota() {
			const totalQuota = (this.userQuota.totalLimit || 0) + (this.userQuota.permanentQuota || 0)
			const used = this.userQuota.currentUseNum || 0
			return Math.max(0, totalQuota - used)
		}
	},
	created() {

		//获取激活标志
		// let tokenFlag = localStorage.getItem('addTokenFlag');
		// if(tokenFlag){
		// 	this.addTokenFlag = tokenFlag
		// }
		getUserFqToken().then(res => {
			if(res.code===200){
				if(res.data){
					this.addTokenFlag = true
					this.tokenName = res.data.tokenName
					if (this.tokenName) {
						this.inputToken = this.tokenName
						this.verifyToken()
					}
				}else {
					this.addTokenFlag = false
				}
			}
		})

		// 从localStorage尝试获取已保存的token
		// const savedToken = localStorage.getItem('fq_token')

		// 获取签到配置
		this.loadSignInConfig()
		// 生成日历数据
		this.generateCalendar()
	},
	methods: {
		encryp( key , iv , data ){
			if( typeof data === "object" ){
				// 如果传入的data是json对象，先转义为json字符串
				try {
					data = JSON.stringify(data)
				} catch (error) {
					console.log("error:",error)
				}
			}
			// 统一将传入的字符串转成UTF8编码
			const dataHex = CryptoJS.enc.Utf8.parse( data ) // 需要加密的数据
			const keyHex = CryptoJS.enc.Utf8.parse( key ) // 秘钥
			const ivHex = CryptoJS.enc.Utf8.parse( iv ) // 偏移量
			const encrypted = CryptoJS.AES.encrypt( dataHex , keyHex , {
				iv: ivHex,
				mode: CryptoJS.mode.CBC, // 加密模式
				padding: CryptoJS.pad.Pkcs7
			})
			let encryptedVal = encrypted.ciphertext.toString()
			return encryptedVal //  返回加密后的值
		},
		getTimesInterval() {
			let year = new Date().getFullYear(); //获取当前时间的年份
			let month = new Date().getMonth() + 1; //获取当前时间的月份
			let day = new Date().getDate(); //获取当前时间的天数
			let hours = new Date().getHours(); //获取当前时间的小时
			let minutes = new Date().getMinutes(); //获取当前时间的分数
			let seconds = new Date().getSeconds(); //获取当前时间的秒数

			//当小于 10 的是时候，在前面加 0
			if (hours < 10) {
				hours = "0" + hours;
			}
			if (minutes < 10) {
				minutes = "0" + minutes;
			}
			if (seconds < 10) {
				seconds = "0" + seconds;
			}
			if (day < 10) {
				day = "0" + day;
			}
			if (month < 10) {
				month = "0" + month;
			}

			//拼接格式化当前时间
			return year + "-" + month + "-" + day  +" "+ hours + ":" + minutes + ":" + seconds ;
		},
		addToken(){
			let time = this.getTimesInterval();
			//激活码生成
			let nowTime = new Date().getTime()
			let tokenName = this.encryp('huswhusbg','wqowwjnsm',nowTime)
			addFqToken(tokenName,time).then(res => {
				if (res.code === 200) {
					this.newToken = tokenName
					this.inputToken = tokenName
					// 显示弹窗而不是直接显示消息
					this.tokenDialogVisible = true

					//存储激活码
					localStorage.setItem('fq_token', this.inputToken)
				}
			})
		},
		// 弹窗关闭后处理
		handleTokenDialogClosed() {
			// 用户关闭弹窗后设置标记，隐藏获取激活码栏
			this.addTokenFlag = true
			// 验证激活码
			this.verifyToken()
		},
		goBack(){
			this.$router.push('/localTool')
		},
		// 签到配置和历史
		loadSignInConfig() {
			getSetting().then(res => {
				if (res.code === 200) {
					this.signInConfig = res.data
				}
			})
		},
		// 加载用户签到状态和历史
		loadUserSignInInfo() {
			if (!this.userInfo) return

			// 检查今日是否已签到
			checkSignInToday(this.userInfo.tokenName).then(res => {
				if (res.code === 200) {
					this.hasSigned = res.data
				} else {
					// 如果check_sign_in接口也有问题，默认为未签到
					this.hasSigned = false
				}
			}).catch(() => {
				this.hasSigned = false
				console.error('检查签到状态失败')
			})

			// 由于/sign_in/user_stats不存在，直接从签到历史中计算连续签到天数
			this.loadSignInHistory(true)
		},
		loadSignInHistory(calculateContinuous = false) {
			if (!this.userInfo) return

			getSettingHistory(this.userInfo.tokenName,
					this.historyPage,
					calculateContinuous ? 30 : this.historyPageSize).then(res => {
				if (res.code === 200) {

					// 设置签到历史记录
					if (!calculateContinuous) {
						this.signInHistory = this.historyPage === 1 ? res.rows : [...this.signInHistory, ...res.rows]
						this.hasMoreHistory = this.signInHistory.length < res.total
					} else {
						// 当需要计算连续签到天数时
						const historyList = res.rows || []

						// 使用签到历史数据生成当月签到日期
						this.generateMonthlyRecord(historyList)

						// 计算连续签到天数
						this.calculateContinuousDays(historyList)

						// 更新历史记录显示
						this.signInHistory = historyList.slice(0, this.historyPageSize)
						this.hasMoreHistory = historyList.length > this.historyPageSize
					}
				}
			}).catch(err => {
				console.error('获取签到历史失败', err)
			})
		},
		// 新增方法：根据签到历史计算连续签到天数
		calculateContinuousDays(historyList) {
			if (!historyList || historyList.length === 0) {
				this.userStats.continuousDays = 0
				return
			}

			// 对签到历史按日期排序（最近的在前）
			historyList.sort((a, b) => new Date(b.signInTime) - new Date(a.signInTime))

			// 获取当前日期（不含时间）
			const today = new Date()
			today.setHours(0, 0, 0, 0)

			let continuousDays = 0
			let lastDate = null

			// 遍历签到记录计算连续天数
			for (let i = 0; i < historyList.length; i++) {
				const signInDate = new Date(historyList[i].signInTime)
				signInDate.setHours(0, 0, 0, 0)

				// 第一条记录
				if (i === 0) {
					// 检查最近的签到是否是今天
					const isToday = signInDate.getTime() === today.getTime()

					if (isToday) {
						continuousDays = 1
						lastDate = signInDate
					} else {
						// 如果最近的签到不是今天，检查是否是昨天
						const yesterday = new Date(today)
						yesterday.setDate(today.getDate() - 1)

						if (signInDate.getTime() === yesterday.getTime()) {
							continuousDays = 1
							lastDate = signInDate
						} else {
							// 最近的签到既不是今天也不是昨天，没有连续签到
							break
						}
					}
				} else {
					// 检查当前记录是否与上一条记录连续
					const expectedDate = new Date(lastDate)
					expectedDate.setDate(expectedDate.getDate() - 1)

					if (signInDate.getTime() === expectedDate.getTime()) {
						continuousDays++
						lastDate = signInDate
					} else {
						// 不连续，结束检查
						break
					}
				}
			}

			this.userStats.continuousDays = continuousDays
		},
		// 新增方法：根据签到历史生成当月签到记录
		generateMonthlyRecord(historyList) {
			if (!historyList || historyList.length === 0) {
				this.signInDates = []
				this.generateCalendar()
				return
			}

			// 筛选当月的签到记录
			const currentYear = this.currentYear
			const currentMonth = this.currentMonth - 1 // JavaScript月份从0开始

			this.signInDates = historyList
					.map(item => new Date(item.signInTime))
					.filter(date => date.getFullYear() === currentYear && date.getMonth() === currentMonth)

			// 重新生成日历数据
			this.generateCalendar()
		},
		// 处理签到
		handleSignIn() {
			if (!this.userInfo || this.hasSigned) return

			doSignIn(this.userInfo.tokenName).then(res => {

				if (res.code === 200) {
					this.$message.success(`签到成功，获得${res.data.rewardAmount}额度`)
					this.hasSigned = true

					// 更新签到信息
					// 增加今天的日期到签到日期列表
					const today = new Date()
					this.signInDates.push(today)

					// 重新生成日历
					this.generateCalendar()

					// 更新连续签到天数
					this.userStats.continuousDays += 1

					// 刷新额度信息
					this.refreshQuota()

					// 更新签到历史
					setTimeout(() => {
						this.historyPage = 1
						this.loadSignInHistory()
					}, 500)
				} else {
					this.$message.error(res.data.msg || '签到失败')
				}
			}).catch(err => {
				console.error('签到失败', err)
				this.$message.error('签到失败，请稍后再试')
			})
		},
		// 验证激活码
		verifyToken() {
			if (!this.inputToken) {
				this.$message.warning('请输入激活码')
				return
			}

			// 获取激活码信息
			getCodeInfo(this.inputToken).then(res => {
				console.log('获取激活码信息成功', res)
				if (res.code === 200) {
					// 保存激活码到localStorage
					localStorage.setItem('fq_token', this.inputToken)

					// 由于可能没有selectByName接口，使用当前返回的数据
					this.userInfo = {
						tokenName: this.inputToken,
						tokenTime: new Date().toISOString().split('T')[0],
						tokenStatus: 0, // 假设状态为正常
						tokenCompCode: '尚未绑定'
					}

					console.log('额度=====', res.data)

					// 保存额度信息
					this.userQuota = res.data || {
						baseLimit: 0,
						activityLimit: 0,
						totalLimit: 0,
						currentUseNum: 0,
						remainingLimit: 0,
						permanentQuota: 0
					}

					// 尝试获取额外的token信息
					getExtraTokenInfo(this.inputToken).then(detailRes => {
						if (detailRes.code === 200 && detailRes.rows && detailRes.rows.length > 0) {
							console.log('获取额外token信息成功', detailRes)
							// 使用查询到的详细信息更新用户信息
							const tokenDetail = detailRes.rows[0]
							this.userInfo = {
								...this.userInfo,
								...tokenDetail
							}
						}

						// 加载签到信息
						this.loadUserSignInInfo()
					}).catch(() => {
						// 即使获取详情失败，也继续加载签到信息
						this.loadUserSignInInfo()
					})
				} else {
					this.$message.error(res.msg || '激活码无效')
				}
			}).catch(err => {
				console.error('验证激活码失败', err)
				this.$message.error('验证激活码失败，请稍后再试')
			})
		},
		// 刷新额度
		refreshQuota() {
			if (!this.userInfo) return

			refQuota(this.userInfo.tokenName).then(res => {
				if (res.code === 200) {
					this.userQuota = res.data
					this.$message.success('额度刷新成功')
				}
			})
		},
		// 退出登录
		logout() {
			this.$confirm('确定要重置激活码吗?重置激活码不会重置额度，该按钮旨在帮助遇见bug用户清空当前的激活码，一般情况不要点！', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				localStorage.removeItem('fq_token')
				this.userInfo = null
				this.userQuota = {
					baseLimit: 0,
					activityLimit: 0,
					tempLimit: 0,
					totalLimit: 0,
					currentUseNum: 0,
					remainingLimit: 0
				}
				this.inputToken = ''
				this.hasSigned = false
				this.signInHistory = []

				//  刷新页面
				window.location.reload()
			}).catch(() => {})
		},
		// 复制文本
		async copyText(text) {
			try {
				if (navigator.clipboard && window.isSecureContext) {
					// 使用现代的Clipboard API
					await navigator.clipboard.writeText(text)
					this.$message.success('复制成功')
				} else {
					// 降级到传统方法
					const input = document.createElement('input')
					input.value = text
					document.body.appendChild(input)
					input.select()
					document.execCommand('copy')
					document.body.removeChild(input)
					this.$message.success('复制成功')
				}
			} catch (err) {
				console.error('复制失败:', err)
				this.$message.error('复制失败，请手动复制')
			}
		},
		// 打开赞助弹窗
		openDonateDialog() {
			this.donateDialogVisible = true
		},
		// 格式化百分比
		percentageFormat(percentage) {
			if (percentage >= 90) return '警告'
			if (percentage >= 70) return '注意'
			return `${percentage}%`
		},
		// 添加缺失的loadMoreHistory方法
		loadMoreHistory() {
			this.historyPage++
			this.loadSignInHistory()
		},
		// 重新添加生成日历数据的方法
		generateCalendar() {
			const year = this.currentYear
			const month = this.currentMonth - 1 // JavaScript月份从0开始

			// 获取当月第一天是星期几
			const firstDay = new Date(year, month, 1).getDay()

			// 获取当月天数
			const daysInMonth = new Date(year, month + 1, 0).getDate()

			const today = new Date()
			const isCurrentMonth = today.getFullYear() === year && today.getMonth() === month

			let dates = []

			// 填充前面的空白
			for (let i = 0; i < firstDay; i++) {
				dates.push({ day: null })
			}

			// 填充日期
			for (let i = 1; i <= daysInMonth; i++) {
				const dateObj = new Date(year, month, i)
				const isToday = isCurrentMonth && today.getDate() === i
				const signed = this.signInDates.some(date =>
						date.getFullYear() === year &&
						date.getMonth() === month &&
						date.getDate() === i
				)

				// 过去的日期和今天可以显示签到状态，未来日期禁用
				const disabled = dateObj > today

				dates.push({
					day: i,
					isToday,
					signed,
					disabled
				})
			}

			this.calendarDates = dates
		}
	}
}
</script>

<style scoped>
.user-signin-container {
	padding: 20px;
}
.signin-card, .token-card {
	height: 100%;
	margin-bottom: 20px;
}
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.card-title {
	font-size: 18px;
	font-weight: bold;
}
.signin-content, .token-content {
	padding: 10px;
}
.signin-info {
	margin-bottom: 20px;
	text-align: center;
}
.signin-status {
	font-size: 16px;
	margin-bottom: 10px;
}
.signin-tip {
	color: #606266;
	font-size: 14px;
	margin: 5px 0;
}
.highlight {
	color: #409EFF;
	font-weight: bold;
	font-size: 18px;
}
.signin-btn {
	width: 100%;
	margin: 20px 0;
}
.signin-history {
	margin-top: 20px;
}
.history-title {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10px;
	font-weight: bold;
}
.no-data {
	text-align: center;
	color: #909399;
	padding: 20px 0;
}
.calendar-container {
	border: 1px solid #EBEEF5;
	border-radius: 4px;
	padding: 10px;
	margin-bottom: 20px;
}
.calendar-title {
	text-align: center;
	font-weight: bold;
	margin-bottom: 10px;
}
.calendar-week {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	text-align: center;
	border-bottom: 1px solid #EBEEF5;
	padding-bottom: 5px;
}
.week-day {
	font-weight: bold;
	color: #606266;
}
.calendar-dates {
	display: grid;
	grid-template-columns: repeat(7, 1fr);
	grid-gap: 5px;
	padding-top: 10px;
}
.calendar-date {
	height: 30px;
	width: 30px;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 50%;
	position: relative;
	margin: 0 auto;
}
.empty {
	visibility: hidden;
}
.signed {
	background-color: #67C23A;
	color: white;
}
.today {
	border: 2px solid #409EFF;
	font-weight: bold;
}
.disabled {
	color: #C0C4CC;
}
.signed-mark {
	position: absolute;
	font-size: 12px;
}
.token-content {
	padding: 20px 0;
}
.no-token {
	text-align: center;
	padding: 30px 0;
}
.no-token i {
	font-size: 48px;
	color: #909399;
	margin-bottom: 20px;
}
.no-token p {
	margin-bottom: 20px;
	color: #606266;
}
.token-input {
	margin-bottom: 20px;
	width: 80%;
}
.token-info {
	margin-top: 20px;
}
.info-item {
	margin-bottom: 15px;
	display: flex;
	align-items: center;
}
.label {
	color: #606266;
	width: 100px;
}
.value {
	font-weight: bold;
}
.value-with-copy {
	display: flex;
	align-items: center;
	flex: 1;
}
.quota-info {
	margin-top: 20px;
}

/* 主要额度显示卡片 */
.main-quota-display {
	margin-bottom: 25px;
}

.quota-card {
	display: flex;
	align-items: center;
	padding: 20px;
	border-radius: 12px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	height: 100px;
}

.quota-card:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.today-usage {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.remaining-total {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: white;
}

.quota-icon {
	font-size: 32px;
	margin-right: 15px;
	opacity: 0.8;
}

.quota-content {
	flex: 1;
}

.quota-card .quota-value {
	font-size: 28px;
	font-weight: bold;
	margin-bottom: 5px;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quota-card .quota-label {
	font-size: 14px;
	opacity: 0.9;
	margin: 0;
}

.quota-sublabel {
	font-size: 12px;
	opacity: 0.7;
	margin-top: 2px;
}

/* 详细额度信息 */
.detailed-quota {
	margin-top: 15px;
}

.quota-item {
	text-align: center;
	padding: 15px 10px;
	background-color: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #e9ecef;
	transition: all 0.3s ease;
}

.quota-item:hover {
	background-color: #e3f2fd;
	border-color: #409EFF;
	transform: translateY(-1px);
}

.quota-item .quota-value {
	font-size: 20px;
	font-weight: bold;
	color: #409EFF;
	margin-bottom: 5px;
}

.quota-item .quota-label {
	font-size: 12px;
	color: #606266;
	margin: 0;
}

.token-actions {
	margin-top: 30px;
	display: flex;
	justify-content: space-around;
}
.token-dialog-content {
	padding: 20px 10px;
	text-align: center;
}
.token-dialog-tip {
	color: #E6A23C;
	font-weight: bold;
	margin-bottom: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.token-dialog-tip i {
	font-size: 18px;
	margin-right: 5px;
}
.token-dialog-code {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20px;
	background-color: #f0f9eb;
	padding: 12px;
	border-radius: 4px;
	border-left: 4px solid #67C23A;
}
.token-dialog-steps {
	margin-top: 30px;
	text-align: left;
	background-color: #f5f7fa;
	padding: 20px;
	border-radius: 8px;
}
.token-dialog-steps-title {
	font-weight: bold;
	margin-bottom: 15px;
	color: #303133;
	display: flex;
	align-items: center;
}
.token-dialog-steps-title i {
	margin-right: 5px;
	color: #409EFF;
}
.token-dialog-steps-content {
	display: flex;
	flex-direction: column;
	gap: 12px;
}
.step-item {
	display: flex;
	align-items: center;
	padding: 10px;
	background-color: white;
	border-radius: 6px;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
	transition: all 0.3s;
}
.step-item:hover {
	transform: translateX(5px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.step-number {
	width: 28px;
	height: 28px;
	background-color: #409EFF;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	margin-right: 12px;
}
.step-text {
	flex: 1;
	font-size: 14px;
}
.step-status {
	margin-left: 12px;
	color: #909399;
}
.step-status.completed {
	color: #67C23A;
}

/* 优化操作指南样式 */
.guide-card {
	margin-bottom: 20px;
	transition: all 0.3s;
}
.guide-card:hover {
	transform: translateY(-3px);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
.guide-content {
	padding: 20px 10px;
}
.custom-steps {
	margin-bottom: 20px;
}
.custom-steps >>> .el-step__title {
	font-weight: bold;
	font-size: 16px;
}
.custom-steps >>> .el-step__description {
	font-size: 14px;
	color: #606266;
}
.custom-steps >>> .el-step__icon {
	background-color: #409EFF;
	color: white;
}
.custom-steps >>> .el-step__head.is-process .el-step__icon {
	background-color: #67C23A;
	box-shadow: 0 0 10px rgba(103, 194, 58, 0.4);
}
.guide-tips {
	margin-top: 20px;
}

/* 赞助弹窗样式 */
.donate-dialog-content {
	padding: 20px 10px;
}

.donate-header {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20px;
}

.donate-header i {
	color: #E6A23C;
	font-size: 20px;
	margin: 0 10px;
}

.donate-header h3 {
	font-size: 20px;
	color: #303133;
	margin: 0;
}

.donate-description {
	background-color: #f8f8f8;
	padding: 15px;
	border-radius: 8px;
	margin-bottom: 20px;
}

.donate-description p {
	margin: 10px 0;
	line-height: 1.5;
}

.donate-description ul {
	list-style: none;
	padding-left: 10px;
	margin: 15px 0;
}

.donate-description li {
	margin: 8px 0;
	color: #606266;
}

.donate-description li i {
	color: #67C23A;
	margin-right: 5px;
}

.donate-tip {
	font-weight: bold;
	color: #E6A23C;
	text-align: center;
	margin-top: 15px;
}

.donate-methods {
	display: flex;
	justify-content: space-around;
	flex-wrap: wrap;
	margin: 20px 0;
}

.donate-method-item {
	width: 45%;
	min-width: 200px;
	margin-bottom: 20px;
	text-align: center;
}

.donate-method-title {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 10px;
}

.donate-method-title i {
	font-size: 18px;
	margin-right: 5px;
}

.donate-method-title span {
	font-size: 16px;
	font-weight: bold;
}

.donate-qr-code {
	width: 200px;
	height: 200px;
	margin: 0 auto;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.alipay-bg {
	background-color: #00a0e9;
	color: white;
}

.wechat-bg {
	background-color: #2aae67;
	color: white;
}

.qr-placeholder {
	font-size: 14px;
	font-weight: bold;
}

.donate-footer {
	border-top: 1px solid #EBEEF5;
	padding-top: 15px;
	text-align: center;
	color: #909399;
	font-size: 12px;
}

.donate-footer p {
	margin: 5px 0;
}

.donate-footer i {
	color: #E6A23C;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.quota-card {
		flex-direction: column;
		text-align: center;
		height: auto;
		padding: 15px;
	}

	.quota-icon {
		margin-right: 0;
		margin-bottom: 10px;
	}

	.quota-card .quota-value {
		font-size: 24px;
	}

	.detailed-quota .el-col {
		margin-bottom: 10px;
	}

	.main-quota-display .el-col {
		margin-bottom: 15px;
	}
}

@media (max-width: 480px) {
	.quota-card .quota-value {
		font-size: 20px;
	}

	.quota-item .quota-value {
		font-size: 18px;
	}

	.quota-icon {
		font-size: 24px;
	}
}
</style>
