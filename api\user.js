import http from '@/utils/request'
import { mockApi, shouldUseMockApi } from '@/utils/mockApi'

// 登录方法
export function loginApi(username, password, code, uuid) {
	const data = {
		username,
		password,
		code,
		uuid
	}

	if (shouldUseMockApi()) {
		return mockApi.login(data)
	}

	return http.post('/login', data, {
		header: {
			isToken: false,
			repeatSubmit: false
		}
	})
}

// 注册方法
export function register(data) {
	if (shouldUseMockApi()) {
		return mockApi.register(data)
	}

	return http.post('/register', data, {
		header: {
			isToken: false
		}
	})
}

// 获取用户详细信息
export function getInfo() {
	if (shouldUseMockApi()) {
		return mockApi.getUserInfo()
	}

	return http.get('/getInfo')
}

// 退出方法
export function logout() {
	if (shouldUseMockApi()) {
		return mockApi.logout()
	}

	return http.post('/logout')
}

// 获取验证码
export function getCodeImg() {
	if (shouldUseMockApi()) {
		return mockApi.getCaptcha()
	}

	return http.get('/captchaImage', {}, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}

// 得到登录页面配置
export function loginInfo() {
	if (shouldUseMockApi()) {
		return mockApi.getLoginInfo()
	}

	return http.get('/login/info', {}, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}

// 发送短信验证码
export function sendSmsCode(phone) {
	if (shouldUseMockApi()) {
		return mockApi.sendSmsCode(phone)
	}

	return http.get('/login/captcha', { phone }, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}

// 手机登录发送短信验证码
export function sendPhoneCode(phone) {
	if (shouldUseMockApi()) {
		return mockApi.sendPhoneCode(phone)
	}

	return http.get('/login/loginCaptcha', { phone }, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}

//手机登录验证码验证
export function phoneLogin(phone, code) {
	if (shouldUseMockApi()) {
		return mockApi.phoneLogin(phone, code)
	}

	return http.post('/login/smsLogin', {
		phoneNumber: phone,
		smsCode: code
	}, {
		header: {
			isToken: false
		},
		timeout: 20000
	})
}