<script>
	import { autoLoginCheck, setupNavigationGuard } from '@/utils/auth'

	export default {
		onLaunch: function() {
			console.log('App Launch')

			// 设置导航守卫
			setupNavigationGuard()

			// 自动登录检查
			this.checkLoginOnLaunch()
		},

		onShow: function() {
			console.log('App Show')
		},

		onHide: function() {
			console.log('App Hide')
		},

		methods: {
			// 应用启动时检查登录状态
			async checkLoginOnLaunch() {
				try {
					console.log('应用启动，暂时跳过自动登录检查')
					// 暂时禁用自动登录检查，让页面自己处理登录验证
					// 延迟一点时间，确保应用完全启动
					// setTimeout(async () => {
					//     const isLoggedIn = await autoLoginCheck()
					//     if (!isLoggedIn) {
					//         console.log('用户未登录，跳转到登录页')
					//     } else {
					//         console.log('用户已登录')
					//     }
					// }, 100)
				} catch (error) {
					console.error('登录检查失败:', error)
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
</style>
