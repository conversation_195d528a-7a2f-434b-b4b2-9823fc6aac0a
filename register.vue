<template>
	<div class="login_root">
		<div class="login_content">
			<div class="content_left"><el-image :src="regImg" style="width: 100%;"></el-image></div>
			<div class="content_right">
				<div class="login_form">
					<div style="width: 100%;text-align: left;margin-bottom: 40px;"><span style="font-weight: bolder;font-size: 20px;">欢迎加入我们</span></div>
					<el-form :model="ruleForm" :rules="rules" ref="ruleForm"  style="width: 100%;">
						<el-form-item prop="username">
							<el-input v-model="ruleForm.username" placeholder="请输入用户名">
								<i slot="prefix" class="el-icon-user ico"/>
							</el-input>
						</el-form-item>
						<el-form-item prop="password">
							<el-input v-model="ruleForm.password" type="password" show-password placeholder="请输入密码">
								<i slot="prefix" class="el-icon-lock ico"/>
							</el-input>
						</el-form-item>
						<el-form-item prop="phone">
							<el-input v-model="ruleForm.phone" placeholder="请输入手机号">
								<i class="el-icon-phone ico" slot="prefix"/>
							</el-input>
						</el-form-item>
						<el-form-item prop="code" v-if="captchaEnabled">
							<div>
								<el-input v-model="ruleForm.code" placeholder="请输入验证码" style="width: 40%;">
									<i slot="prefix" class="el-icon-circle-check ico"/>
								</el-input>
								<div class="login-code ">
									<img :src="codeUrl" @click="getCode" class="login-code-img"/>
								</div>
							</div>
						</el-form-item>
						<el-form-item prop="phoneCode">
							<el-row :gutter="20">
								<el-col :span="14">
									<el-input v-model="ruleForm.phoneCode" placeholder="请输入短信验证码">
										<i slot="prefix" class="el-icon-circle-check ico"/>
									</el-input>
								</el-col>
								<el-col :span="10">
									<el-button type="primary" @click="sendSmsCode" style="width: 100%;border: none !important;">{{captchaBtnStr}}</el-button>
								</el-col>
							</el-row>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" :loading="loading" @click="handleRegister" style="width: 100%;border: none !important;">
								<span v-if="!loading">注 册</span>
								<span v-else>注 册 中...</span>
							</el-button>
							<div style="width: 100%;text-align: right; font-size: small;color: #409eff;cursor: pointer;" @click="returnLogin"><span>返回</span></div>
						</el-form-item>
					</el-form>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import registerImg from '../../assets/images/login/register.png'
import { register, getCodeImg, sendSmsCode } from '@/api/loginApi'
export default {
	name: "register",
	data() {
		return {
			regImg:registerImg,
			loading: false,
			ruleForm: {
				username: '',
				password: '',
				code:''
			},
			rules: {
				username: [
					{ required: true, message: '请输入用户名', trigger: 'blur' },
					{ min: 4, max: 12, message: '长度在4到12个字符之间', trigger: 'blur' },
					{ pattern: /^[a-zA-Z]{4,12}$/, message: '用户名只能包含英文字母，长度在4到12个字符之间', trigger: 'blur' }
				],
				password: [
					{ required: true, message: '请输入密码', trigger: 'blur' },
					{ min: 6, message: '长度不少于 6 个字符', trigger: 'blur' }
				],
				phone: [
					{ required: true, message: '请输入手机号', trigger: 'blur' },
					{ len: 11, message: '请输入正确的手机号', trigger: 'blur' }
				],
				phoneCode: [
					{ required: true, message: '请输入短信验证码', trigger: 'blur' },
					{ min: 6, message: '长度不少于 6 个字符', trigger: 'blur' }
				],
				code: [
					{ required: true, message: '请输入验证码', trigger: 'blur'}
				]
			},
			// 验证码开关
			captchaEnabled: true,
			codeUrl:'',
			captchaBtnStr: "获取验证码"
		}
	},
	created() {
		// 获取验证码
		this.getCode();
	},
	mounted() {

	},
	methods: {
		handleRegister() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					this.loading = true;
					register(this.ruleForm).then(res => {
						const username = this.ruleForm.username;
						this.$alert("<font color='red'>恭喜你，您的账号 " + username + " 注册成功！</font>", '系统提示', {
							dangerouslyUseHTMLString: true,
							type: 'success'
						}).then(() => {
							this.$router.push("/login");
						}).catch(() => {});
					}).catch(() => {
						this.loading = false;
						if (this.captchaEnabled) {
							this.getCode();
						}
					})
				}
			});
		},
		getCode() {
			getCodeImg().then(res => {
				this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
				if (this.captchaEnabled) {
					this.codeUrl = "data:image/gif;base64," + res.img;
					this.ruleForm.uuid = res.uuid;
				}
			});
		},
		// 发送短信验证码
		sendSmsCode() {
			if(this.ruleForm.phone !== "" && this.ruleForm.phone !== undefined) {
				sendSmsCode(this.ruleForm.phone).then(res => {
					if(res.msg === "发送成功") {
						this.$message.success("发送成功");
						this.captchaBtnDisable = true;
						let timer = 60, minutes, seconds;
						const interval = setInterval(() => {
							minutes = Math.floor(timer / 60);
							seconds = timer % 60;
							seconds = seconds < 10 ? '0' + seconds : seconds;
							this.captchaBtnStr = `${seconds} 秒后重新发送`;

							if (--timer < 0) {
								clearInterval(interval);
								this.captchaBtnStr = "获取验证码";
								this.captchaBtnDisable = false;
							}
						}, 1000);
					} else {
						this.$message.error(res.msg);
					}
				});
			} else {
				this.$message.error("请填写手机号！");
			}
		},
		returnLogin(){
			// 清除校验结果
			this.$refs.ruleForm.clearValidate();
			this.$router.push({path: '/login'});
		},
	}
}
</script>

<style scoped>
.login_root{
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #fff;
	/*border: 1px solid red;*/
}
.login_content{
	width: 70vw;
	height: 70vh;
	/*border: 1px solid #000;*/
	display: flex;
}
.content_left{
	width: 50%;
	display: flex;
	align-items: center;
	/*border: 1px solid #000;*/
}

.content_right{
	width: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	/*border: 1px solid #000;*/
}

.login_form{
	width: 80%;
	padding: 30px;
	border: 1px solid #dedede;
	box-sizing: border-box;
	border-radius: 10px;
	display: flex;
	flex-direction: column;
	align-items: start;
	box-shadow: 0 2px 12px 0 rgb(0 0 0 / 20%);
}

.ico{
	font-size: 18px;
	margin-top: 10px;
}

.login-code-img {
	height: 38px;
}

.login-code {
	width: 50%;
	height: 38px;
	float: right;
}

.login-code img {
	cursor: pointer;
	vertical-align: middle;
}
</style>
