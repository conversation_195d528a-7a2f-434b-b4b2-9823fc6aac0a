// 测试登录页面密码记住功能的加密解密
const { base64Encode, base64Decode } = require('./base64-test.js');

// 模拟utils对象 (与api/index.js中的实现一致)
const utils = {
    base64Encode(str) {
        try {
            return base64Encode(String(str));
        } catch (error) {
            console.error('Base64编码失败:', error);
            return String(str);
        }
    },

    base64Decode(str) {
        try {
            return base64Decode(String(str));
        } catch (error) {
            console.error('Base64解码失败:', error);
            return String(str);
        }
    },

    simpleEncode(str) {
        try {
            str = String(str);
            let result = '';
            for (let i = 0; i < str.length; i++) {
                const hex = str.charCodeAt(i).toString(16);
                result += hex.padStart(2, '0');
            }
            return result;
        } catch (error) {
            console.error('简单编码失败:', error);
            return String(str);
        }
    },

    simpleDecode(hexStr) {
        try {
            hexStr = String(hexStr);
            let result = '';
            for (let i = 0; i < hexStr.length; i += 2) {
                const hex = hexStr.substr(i, 2);
                const charCode = parseInt(hex, 16);
                if (!isNaN(charCode)) {
                    result += String.fromCharCode(charCode);
                }
            }
            return result;
        } catch (error) {
            console.error('简单解码失败:', error);
            return String(hexStr);
        }
    },

    encrypt(data, key = 'default_key', iv = 'default_iv') {
        try {
            if (typeof data === "object") {
                try {
                    data = JSON.stringify(data);
                } catch (error) {
                    console.log("JSON序列化失败:", error);
                    return data;
                }
            }

            const dataStr = String(data);
            const keyStr = key + iv;
            let encrypted = '';

            for (let i = 0; i < dataStr.length; i++) {
                const dataChar = dataStr.charCodeAt(i);
                const keyChar = keyStr.charCodeAt(i % keyStr.length);
                const encryptedChar = dataChar ^ keyChar;
                encrypted += String.fromCharCode(encryptedChar);
            }

            return this.base64Encode(encrypted);
        } catch (error) {
            console.error('加密失败:', error);
            try {
                return this.simpleEncode(String(data));
            } catch (fallbackError) {
                console.error('降级编码也失败:', fallbackError);
                return String(data);
            }
        }
    },

    decrypt(encryptedData, key = 'default_key', iv = 'default_iv') {
        try {
            if (!encryptedData) return '';

            let decoded;
            try {
                decoded = this.base64Decode(encryptedData);
            } catch (base64Error) {
                console.error('Base64解码失败，尝试简单解码:', base64Error);
                decoded = this.simpleDecode(encryptedData);
            }

            if (!decoded) return encryptedData;

            const keyStr = key + iv;
            let decrypted = '';

            for (let i = 0; i < decoded.length; i++) {
                const encryptedChar = decoded.charCodeAt(i);
                const keyChar = keyStr.charCodeAt(i % keyStr.length);
                const decryptedChar = encryptedChar ^ keyChar;
                decrypted += String.fromCharCode(decryptedChar);
            }

            return decrypted;
        } catch (error) {
            console.error('解密失败:', error);
            return String(encryptedData);
        }
    }
};

// 模拟登录页面的密码保存和加载过程
function simulatePasswordSaveLoad() {
    console.log('=== 模拟登录页面密码记住功能 ===');
    
    // 模拟用户输入的密码
    const testPasswords = [
        'password123',
        'mySecretPassword!@#',
        '中文密码测试',
        'P@ssw0rd2024',
        'simple',
        '复杂密码123!@#$%^&*()',
        ''
    ];
    
    let allPassed = true;
    
    testPasswords.forEach((password, index) => {
        try {
            console.log(`\n测试密码 ${index + 1}: "${password}"`);
            
            // 模拟保存密码过程 (登录页面中的saveRememberedPassword方法)
            let encryptedPassword;
            try {
                encryptedPassword = utils.encrypt(password);
                console.log('密码加密成功');
                console.log('加密结果:', encryptedPassword);
            } catch (error) {
                console.error('密码加密失败，使用原始密码存储:', error);
                encryptedPassword = password;
            }
            
            // 模拟从存储中读取密码过程 (登录页面中的loadRememberedPassword方法)
            let decryptedPassword;
            try {
                decryptedPassword = utils.decrypt(encryptedPassword);
                console.log('密码解密成功');
                console.log('解密结果:', decryptedPassword);
            } catch (error) {
                console.error('密码解密失败:', error);
                decryptedPassword = encryptedPassword;
            }
            
            // 验证结果
            const passed = password === decryptedPassword;
            console.log('密码验证:', passed ? '✅ 成功' : '❌ 失败');
            
            if (!passed) {
                console.error('原始密码:', password);
                console.error('解密密码:', decryptedPassword);
                allPassed = false;
            }
            
        } catch (error) {
            console.error(`测试密码 ${index + 1} 出错:`, error.message);
            allPassed = false;
        }
    });
    
    console.log('\n=== 登录密码功能测试总结 ===');
    console.log('总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败');
    
    return allPassed;
}

// 测试边界情况
function testEdgeCases() {
    console.log('\n=== 边界情况测试 ===');
    
    let allPassed = true;
    
    // 测试空字符串
    try {
        const empty = '';
        const encryptedEmpty = utils.encrypt(empty);
        const decryptedEmpty = utils.decrypt(encryptedEmpty);
        const emptyPassed = empty === decryptedEmpty;
        console.log('空字符串测试:', emptyPassed ? '✅ 通过' : '❌ 失败');
        if (!emptyPassed) allPassed = false;
    } catch (error) {
        console.error('空字符串测试失败:', error);
        allPassed = false;
    }
    
    // 测试null和undefined
    try {
        const nullEncrypted = utils.encrypt(null);
        const nullDecrypted = utils.decrypt(nullEncrypted);
        console.log('null测试 - 加密:', nullEncrypted, '解密:', nullDecrypted);
    } catch (error) {
        console.error('null测试失败:', error);
    }
    
    // 测试非法输入
    try {
        const invalidDecrypted = utils.decrypt('invalid_base64_string!!!');
        console.log('非法输入测试 - 解密结果:', invalidDecrypted);
    } catch (error) {
        console.error('非法输入测试失败:', error);
    }
    
    return allPassed;
}

// 运行所有测试
console.log('开始测试登录页面密码记住功能...\n');

const passwordTestResult = simulatePasswordSaveLoad();
const edgeCaseResult = testEdgeCases();

console.log('\n=== 最终测试报告 ===');
console.log('密码功能测试:', passwordTestResult ? '✅ 通过' : '❌ 失败');
console.log('边界情况测试:', edgeCaseResult ? '✅ 通过' : '❌ 失败');

const overallResult = passwordTestResult && edgeCaseResult;
console.log('总体结果:', overallResult ? '✅ 登录密码功能正常' : '❌ 存在问题需要修复');

if (overallResult) {
    console.log('\n🎉 恭喜！Base64加密解密问题已经修复！');
    console.log('现在登录页面的"记住密码"功能应该可以正常工作了。');
} else {
    console.log('\n⚠️  仍然存在一些问题，需要进一步调试。');
}
