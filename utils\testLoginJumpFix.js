// 测试登录跳转修复
import { getToken, setToken, removeToken, isLoggedIn, getUserInfo, setUserInfo } from '@/utils/auth'

// 模拟完整的登录到跳转流程
export function testCompleteLoginJumpFlow() {
    console.log('=== 测试完整登录跳转流程 ===')
    
    return new Promise((resolve) => {
        // 1. 清除现有登录状态
        console.log('1. 清除现有登录状态')
        removeToken()
        
        // 2. 模拟登录API响应
        const mockLoginResponse = {
            code: 200,
            token: 'test_token_' + Date.now(),
            data: {
                id: 1,
                username: 'testuser',
                phone: '13800138000',
                email: '<EMAIL>'
            }
        }
        
        console.log('2. 模拟登录API响应成功')
        
        // 3. 模拟登录成功的保存逻辑
        try {
            const token = mockLoginResponse.token
            console.log('3. 开始保存登录信息')
            console.log('   - Token:', token)
            
            // 保存token和用户信息
            setToken(token)
            setUserInfo(mockLoginResponse.data)
            
            // 验证保存结果
            const savedToken = uni.getStorageSync('user_token')
            const savedUserInfo = uni.getStorageSync('user_info')
            
            console.log('3. 验证保存结果:')
            console.log('   - 保存的Token:', savedToken)
            console.log('   - 保存的UserInfo:', savedUserInfo)
            
            if (!savedToken || !savedUserInfo) {
                throw new Error('登录信息保存失败')
            }
            
            // 4. 检查登录状态
            const loginStatus = isLoggedIn()
            console.log('4. 登录状态检查:', loginStatus)
            
            // 5. 模拟跳转前的验证（类似登录页面的逻辑）
            setTimeout(() => {
                const finalToken = uni.getStorageSync('user_token')
                const finalUserInfo = uni.getStorageSync('user_info')
                
                console.log('5. 跳转前最终验证:')
                console.log('   - Final Token:', finalToken)
                console.log('   - Final UserInfo:', finalUserInfo)
                
                const canJump = !!(finalToken && finalUserInfo)
                console.log('   - 可以跳转:', canJump)
                
                resolve({
                    success: true,
                    canJump: canJump,
                    token: finalToken,
                    userInfo: finalUserInfo,
                    loginStatus: isLoggedIn()
                })
            }, 100)
            
        } catch (error) {
            console.error('登录流程测试失败:', error)
            resolve({
                success: false,
                error: error.message
            })
        }
    })
}

// 测试首页的登录检查逻辑
export function testIndexPageLoginCheck() {
    console.log('\n=== 测试首页登录检查逻辑 ===')
    
    // 确保有登录状态
    const testToken = 'test_token_for_index_' + Date.now()
    const testUserInfo = {
        id: 1,
        username: 'indexTestUser',
        phone: '13800138000'
    }
    
    console.log('1. 设置测试登录状态')
    setToken(testToken)
    setUserInfo(testUserInfo)
    
    // 模拟首页的检查逻辑
    console.log('2. 模拟首页登录检查')
    const token = getToken()
    const userInfo = getUserInfo()
    const loginStatus = isLoggedIn()
    
    console.log('   - Token:', token)
    console.log('   - UserInfo:', userInfo)
    console.log('   - 登录状态:', loginStatus)
    
    return {
        hasToken: !!token,
        hasUserInfo: !!userInfo,
        isLoggedIn: loginStatus,
        canAccessIndex: loginStatus
    }
}

// 测试token一致性
export function testTokenConsistency() {
    console.log('\n=== 测试Token一致性 ===')
    
    const testToken = 'consistency_test_token_' + Date.now()
    
    console.log('1. 保存测试token:', testToken)
    setToken(testToken)
    
    // 检查不同方式获取的token是否一致
    const authToken = getToken()
    const directToken = uni.getStorageSync('user_token')
    
    console.log('2. 验证token一致性:')
    console.log('   - auth.js获取:', authToken)
    console.log('   - 直接获取:', directToken)
    console.log('   - 一致性:', authToken === directToken)
    
    return {
        authToken: authToken,
        directToken: directToken,
        consistent: authToken === directToken
    }
}

// 运行所有测试
export async function runAllLoginJumpTests() {
    console.log('开始运行登录跳转修复测试...\n')
    
    const completeFlowResult = await testCompleteLoginJumpFlow()
    const indexCheckResult = testIndexPageLoginCheck()
    const tokenConsistencyResult = testTokenConsistency()
    
    console.log('\n=== 测试结果汇总 ===')
    console.log('完整登录流程:', completeFlowResult)
    console.log('首页登录检查:', indexCheckResult)
    console.log('Token一致性:', tokenConsistencyResult)
    
    const allSuccess = completeFlowResult.success && 
                      completeFlowResult.canJump && 
                      indexCheckResult.canAccessIndex && 
                      tokenConsistencyResult.consistent
    
    console.log('\n=== 最终结果 ===')
    console.log('所有测试:', allSuccess ? '✅ 通过' : '❌ 失败')
    
    if (allSuccess) {
        console.log('\n🎉 恭喜！登录跳转问题已经修复！')
        console.log('修复要点:')
        console.log('1. ✅ Token存储一致性已修复')
        console.log('2. ✅ 登录成功后延迟验证已添加')
        console.log('3. ✅ 首页登录检查已优化')
        console.log('4. ✅ 路由守卫临时禁用机制已添加')
        console.log('5. ✅ App.vue自动登录检查已禁用')
    } else {
        console.log('\n⚠️  仍然存在一些问题:')
        if (!completeFlowResult.success) {
            console.log('- 登录流程存在问题')
        }
        if (!completeFlowResult.canJump) {
            console.log('- 跳转验证失败')
        }
        if (!indexCheckResult.canAccessIndex) {
            console.log('- 首页访问检查失败')
        }
        if (!tokenConsistencyResult.consistent) {
            console.log('- Token一致性问题')
        }
    }
    
    return {
        completeFlow: completeFlowResult,
        indexCheck: indexCheckResult,
        tokenConsistency: tokenConsistencyResult,
        overall: allSuccess
    }
}

// 清理测试数据
export function cleanupTestData() {
    console.log('清理测试数据...')
    removeToken()
    console.log('测试数据清理完成')
}
