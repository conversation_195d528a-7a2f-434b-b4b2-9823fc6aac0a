# 小说下载器 UniApp 版本

这是一个基于 UniApp 开发的小说下载器应用，支持激活码管理和每日签到功能。

## 功能特性

### 👤 用户系统
- 用户注册和登录
- 密码登录和手机验证码登录
- 用户信息管理
- 记住密码功能
- **登录验证机制** - 必须登录后才能使用小程序功能
- 自动登录检查和路由守卫

### 🔑 激活码管理
- 获取新的激活码
- 验证激活码有效性
- 查看额度使用情况
- 管理激活码状态

### 📅 每日签到
- 每日签到获取额度
- 签到日历显示
- 连续签到奖励
- 签到历史记录

### 🔒 安全特性
- **强制登录验证** - 应用启动时自动检查登录状态
- **路由守卫** - 拦截未登录用户访问受保护页面
- **自动跳转** - 未登录时自动跳转到登录页面
- **登录状态管理** - 统一的用户状态管理和持久化

### 🎨 界面特性
- 现代化的 UI 设计
- 流畅的动画效果
- 响应式布局
- 渐变背景和卡片设计

## 快速开始

### 1. 环境要求
- HBuilderX 3.0+
- Node.js 14+
- 微信开发者工具（如需发布到微信小程序）

### 2. 安装依赖
```bash
npm install
```

### 3. 配置 API 地址
在 `config/index.js` 文件中配置您的 API 基础地址：

```javascript
const config = {
	development: {
		baseURL: 'https://your-actual-api-domain.com/api', // 替换为您的实际 API 地址
		debug: true,
		timeout: 10000
	},
	production: {
		baseURL: 'https://your-actual-api-domain.com/api', // 替换为您的实际 API 地址
		debug: false,
		timeout: 10000
	}
}
```

### 4. 运行项目
- 在 HBuilderX 中打开项目
- 选择运行到微信小程序开发者工具
- 或运行到其他平台

## 项目结构

```
├── pages/                  # 页面文件
│   ├── index/              # 主页面
│   ├── login/              # 登录页面
│   ├── register/           # 注册页面
│   ├── activation/         # 激活码管理页面
│   └── signin/             # 签到页面
├── api/                    # API 接口
│   ├── index.js            # 激活码和签到 API
│   └── user.js             # 用户相关 API
├── utils/                  # 工具函数
│   ├── request.js          # 网络请求封装
│   └── mockApi.js          # 模拟 API（开发用）
├── config/                 # 配置文件
└── static/                 # 静态资源
```

## API 接口说明

### 用户相关接口
- `POST /login` - 用户登录
- `POST /register` - 用户注册
- `GET /getInfo` - 获取用户信息
- `POST /logout` - 用户退出
- `GET /captchaImage` - 获取图形验证码
- `GET /login/captcha` - 发送注册短信验证码
- `GET /login/loginCaptcha` - 发送登录短信验证码
- `POST /login/smsLogin` - 手机验证码登录

### 激活码相关接口
- `GET /fq_token/get_quota` - 获取激活码额度信息
- `GET /fq_token/selectByPage` - 获取激活码详细信息
- `PUT /fq_token/insert` - 新增激活码
- `GET /fq_token/get_user_token` - 获取用户当前token状态

### 签到相关接口
- `GET /sign_in/setting` - 获取签到配置
- `GET /sign_in/check_sign_in` - 检查今日签到状态
- `GET /sign_in/history` - 获取签到历史
- `POST /sign_in/do_sign_in` - 执行签到

## 开发模式

当 API 地址未配置或使用占位符时，应用会自动启用模拟 API 模式，提供以下功能：
- 模拟用户登录和注册（用户名/密码：任意，验证码：1234，短信验证码：123456）
- 模拟激活码生成和验证
- 模拟签到功能
- 模拟数据存储
- 完整的功能演示

### 模拟账号信息
- **图形验证码**：1234
- **短信验证码**：123456
- **用户名/密码**：任意（非空即可）
- **手机号**：任意11位数字

## 部署说明

### 微信小程序
1. 在 `manifest.json` 中配置小程序 appid
2. 在微信开发者工具中预览和上传

### H5
1. 运行到浏览器进行测试
2. 构建后部署到服务器

### App
1. 配置相应的证书和签名
2. 打包发布到应用商店

## 注意事项

1. **API 配置**：请确保在生产环境中配置正确的 API 地址
2. **网络权限**：确保应用有网络访问权限
3. **存储权限**：应用需要本地存储权限来保存激活码等信息
4. **兼容性**：已解决微信小程序中 URLSearchParams 不兼容的问题

## 故障排除

### 常见问题

**Q: 出现 "URLSearchParams is not defined" 错误**
A: 已修复此问题，使用手动构建查询字符串的方式替代 URLSearchParams

**Q: 出现 "uni.base64Encode is not a function" 错误**
A: 已修复此问题，提供了兼容微信小程序的自定义 Base64 编码解码实现

**Q: Base64 编码中文字符出现 undefined 或乱码**
A: 已修复此问题，Base64 实现现在正确支持 Unicode 字符（包括中文、emoji 等）

**Q: API 请求失败**
A: 请检查 config/index.js 中的 API 地址配置是否正确

**Q: 激活码验证失败**
A: 请确保后端 API 接口正常运行，或使用模拟模式进行测试

## 使用说明

### 🚀 **应用流程**

1. **应用启动** → 自动检查登录状态
2. **未登录** → 跳转到登录页面
3. **完成登录** → 进入主界面
4. **功能使用** → 激活码管理、每日签到等
5. **退出登录** → 清除登录信息，返回登录页面

### 📱 **登录验证机制**

- **强制登录**：应用启动时自动检查登录状态，未登录用户无法访问功能页面
- **路由守卫**：自动拦截未登录用户访问受保护页面
- **自动跳转**：未登录时自动跳转到登录页面
- **状态持久化**：登录状态本地存储，重启应用后保持登录状态

### 🔧 **开发和生产模式**

- **开发模式**：使用模拟 API，可直接体验完整功能
- **生产模式**：配置真实 API 地址后连接后端服务
- **模拟账号**：图形验证码 `1234`，短信验证码 `123456`

## 更新日志

### v1.1.6
- **清理登录界面，移除测试代码**
- 移除登录页面中的加密解密测试功能
- 简化登录界面，提升用户体验
- 保留测试工具文件供开发调试使用
- 登录界面更加简洁专业

### v1.1.5
- **修复 Base64 编码 Unicode 字符问题**
- 解决中文字符编码时出现 `undefined` 和 `NaN` 的问题
- 添加完整的 UTF-8 编码解码支持
- 支持 emoji 和其他 Unicode 字符的正确编码
- 新增专门的 Base64 修复测试工具
- 确保加密解密功能完全正常

### v1.1.4
- **集成 utils/base64.js 的 Base64 实现**
- 使用项目中现有的 Base64 编码解码方法
- 优化加密解密系统，优先使用原生 API，降级到自定义实现
- 新增快速测试工具，验证 Base64 和加密解密功能
- 完善的错误处理和降级机制
- 确保与现有代码的完全兼容

### v1.1.3
- **完全重构加密解密系统**
- 使用十六进制编码替代复杂的 Base64 实现
- 采用简单可靠的异或加密算法
- 修复微信小程序 `wx.getSystemInfoSync` 弃用警告
- 新增完整的加密解密测试套件
- 确保所有平台的兼容性和稳定性

### v1.1.2
- **修复登录成功后无法跳转主页的问题**
- 重构加密解密算法，使用更可靠的凯撒密码
- 改进 Base64 编码解码的错误处理
- 优化登录流程，确保即使加密失败也能正常登录
- 新增加密解密测试页面，方便调试
- 增强错误日志和调试信息

### v1.1.1
- **修复微信小程序兼容性问题**
- 修复 `uni.base64Encode is not a function` 错误
- 提供自定义 Base64 编码解码实现
- 改进密码加密解密的错误处理
- 增强微信小程序环境兼容性

### v1.1.0
- **新增强制登录验证机制**
- 应用启动时自动检查登录状态
- 路由守卫拦截未登录用户
- 用户菜单和退出登录功能
- 完善的用户状态管理

### v1.0.0
- 初始版本发布
- 支持激活码管理功能
- 支持每日签到功能
- 现代化 UI 设计
- 微信小程序兼容性修复

## 技术支持

如有问题，请提交 Issue 或联系开发团队。
