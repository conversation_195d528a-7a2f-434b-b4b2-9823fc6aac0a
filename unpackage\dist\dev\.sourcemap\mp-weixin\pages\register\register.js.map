{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?5449", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?21b4", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?cf5b", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?6d5f", "uni-app:///pages/register/register.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?d3da", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/register/register.vue?9ee3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "registerForm", "username", "phone", "phoneCode", "password", "confirmPassword", "code", "uuid", "agreement", "registerLoading", "showPassword", "showConfirmPassword", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "codeUrl", "smsDisabled", "smsButtonText", "smsCountdown", "showAgreementModal", "showPrivacyModal", "computed", "passwordStrength", "passwordStrengthText", "canRegister", "onLoad", "methods", "initPage", "togglePassword", "toggleConfirmPassword", "toggleAgreement", "refreshCaptcha", "res", "console", "sendSmsCode", "uni", "title", "icon", "startSmsCountdown", "clearInterval", "handleRegister", "content", "showCancel", "success", "validateForm", "showAgreement", "closeAgreementModal", "showPrivacy", "closePrivacyModal", "goToLogin"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0rB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8J9sB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA,qCACA,2BACA,+BACA,8BACA,qCACA,gCACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAL;gBACA;kBACAG;oBACAC;oBACAC;kBACA;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACAE;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MAEA;QACA;QACA;QAEA;UACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGA;kBACArC;kBACAC;kBACAC;kBACAC;kBACAE;kBACAC;gBACA;cAAA;gBAPAuB;gBAAA,MASAA;kBAAA;kBAAA;gBAAA;gBACAG;kBACAC;kBACAK;kBACAC;kBACAC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAR;kBACAC;kBACAC;gBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAJ;gBACAE;kBACAC;kBACAC;gBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAGA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACA;QACAT;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAQ;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAd;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9fA;AAAA;AAAA;AAAA;AAAugC,CAAgB,i+BAAG,EAAC,C;;;;;;;;;;;ACA3hC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/register/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/register/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=891c2434&scoped=true&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"891c2434\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/register/register.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=template&id=891c2434&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"register-container\">\n\t\t<!-- 背景装饰 -->\n\t\t<view class=\"bg-decoration\">\n\t\t\t<view class=\"circle circle-1\"></view>\n\t\t\t<view class=\"circle circle-2\"></view>\n\t\t\t<view class=\"circle circle-3\"></view>\n\t\t</view>\n\n\t\t<!-- 注册卡片 -->\n\t\t<view class=\"register-card fade-in\">\n\t\t\t<!-- 头部 -->\n\t\t\t<view class=\"register-header\">\n\t\t\t\t<view class=\"logo\">📝</view>\n\t\t\t\t<view class=\"title\">创建新账户</view>\n\t\t\t\t<view class=\"subtitle\">请填写以下信息完成注册</view>\n\t\t\t</view>\n\n\t\t\t<!-- 注册表单 -->\n\t\t\t<view class=\"form-container\">\n\t\t\t\t<!-- 用户名 -->\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<view class=\"input-icon\">👤</view>\n\t\t\t\t\t<input v-model=\"registerForm.username\" placeholder=\"请输入用户名\" class=\"input-field\" />\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 手机号 -->\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<view class=\"input-icon\">📱</view>\n\t\t\t\t\t<input v-model=\"registerForm.phone\" placeholder=\"请输入手机号\" class=\"input-field\" type=\"number\" />\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 短信验证码 -->\n\t\t\t\t<view class=\"input-group sms-group\">\n\t\t\t\t\t<view class=\"input-icon\">💬</view>\n\t\t\t\t\t<input v-model=\"registerForm.phoneCode\" placeholder=\"请输入短信验证码\" class=\"input-field sms-input\" />\n\t\t\t\t\t<button class=\"sms-btn\" :disabled=\"smsDisabled\" @click=\"sendSmsCode\">\n\t\t\t\t\t\t{{ smsButtonText }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 密码 -->\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<view class=\"input-icon\">🔒</view>\n\t\t\t\t\t<input v-model=\"registerForm.password\" placeholder=\"请输入密码\" class=\"input-field\" \n\t\t\t\t\t\t:password=\"!showPassword\" />\n\t\t\t\t\t<view class=\"input-suffix\" @click=\"togglePassword\">\n\t\t\t\t\t\t{{ showPassword ? '🙈' : '👁️' }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 确认密码 -->\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<view class=\"input-icon\">🔐</view>\n\t\t\t\t\t<input v-model=\"registerForm.confirmPassword\" placeholder=\"请确认密码\" class=\"input-field\" \n\t\t\t\t\t\t:password=\"!showConfirmPassword\" />\n\t\t\t\t\t<view class=\"input-suffix\" @click=\"toggleConfirmPassword\">\n\t\t\t\t\t\t{{ showConfirmPassword ? '🙈' : '👁️' }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 图形验证码 -->\n\t\t\t\t<view v-if=\"captchaEnabled\" class=\"input-group captcha-group\">\n\t\t\t\t\t<view class=\"input-icon\">🔢</view>\n\t\t\t\t\t<input v-model=\"registerForm.code\" placeholder=\"请输入验证码\" class=\"input-field captcha-input\" />\n\t\t\t\t\t<view class=\"captcha-image\" @click=\"refreshCaptcha\">\n\t\t\t\t\t\t<image v-if=\"codeUrl\" :src=\"codeUrl\" class=\"captcha-img\" mode=\"aspectFit\" />\n\t\t\t\t\t\t<view v-else class=\"captcha-placeholder\">点击获取</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 密码强度提示 -->\n\t\t\t\t<view v-if=\"registerForm.password\" class=\"password-strength\">\n\t\t\t\t\t<view class=\"strength-label\">密码强度：</view>\n\t\t\t\t\t<view class=\"strength-bar\">\n\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: passwordStrength >= 1 }\"></view>\n\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: passwordStrength >= 2 }\"></view>\n\t\t\t\t\t\t<view class=\"strength-item\" :class=\"{ active: passwordStrength >= 3 }\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"strength-text\">{{ passwordStrengthText }}</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 用户协议 -->\n\t\t\t\t<view class=\"agreement-group\">\n\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: registerForm.agreement }\" @click=\"toggleAgreement\">\n\t\t\t\t\t\t<view class=\"checkbox-icon\">{{ registerForm.agreement ? '✓' : '' }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"agreement-text\">\n\t\t\t\t\t\t我已阅读并同意\n\t\t\t\t\t\t<text class=\"agreement-link\" @click=\"showAgreement\">《用户协议》</text>\n\t\t\t\t\t\t和\n\t\t\t\t\t\t<text class=\"agreement-link\" @click=\"showPrivacy\">《隐私政策》</text>\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 注册按钮 -->\n\t\t\t\t<button class=\"register-btn\" :class=\"{ loading: registerLoading }\" :disabled=\"registerLoading || !canRegister\" \n\t\t\t\t\t@click=\"handleRegister\">\n\t\t\t\t\t<view v-if=\"registerLoading\" class=\"loading-icon\">⏳</view>\n\t\t\t\t\t{{ registerLoading ? '注册中...' : '立即注册' }}\n\t\t\t\t</button>\n\t\t\t</view>\n\n\t\t\t<!-- 底部操作 -->\n\t\t\t<view class=\"register-footer\">\n\t\t\t\t<view class=\"footer-link\" @click=\"goToLogin\">\n\t\t\t\t\t已有账号？立即登录\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 用户协议弹窗 -->\n\t\t<view v-if=\"showAgreementModal\" class=\"modal-overlay\" @click=\"closeAgreementModal\">\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"modal-title\">用户协议</view>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeAgreementModal\">×</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-body\">\n\t\t\t\t\t<view class=\"agreement-content\">\n\t\t\t\t\t\t<text>1. 用户在使用本服务时，必须遵守相关法律法规。</text>\n\t\t\t\t\t\t<text>2. 用户应当保护好自己的账号和密码，不得将账号借给他人使用。</text>\n\t\t\t\t\t\t<text>3. 用户不得利用本服务进行任何违法违规活动。</text>\n\t\t\t\t\t\t<text>4. 本服务保留对用户行为进行监督和管理的权利。</text>\n\t\t\t\t\t\t<text>5. 如有违反本协议的行为，本服务有权终止用户的使用权限。</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"closeAgreementModal\">我知道了</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 隐私政策弹窗 -->\n\t\t<view v-if=\"showPrivacyModal\" class=\"modal-overlay\" @click=\"closePrivacyModal\">\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"modal-title\">隐私政策</view>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closePrivacyModal\">×</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-body\">\n\t\t\t\t\t<view class=\"privacy-content\">\n\t\t\t\t\t\t<text>1. 我们承诺保护用户的个人隐私信息。</text>\n\t\t\t\t\t\t<text>2. 用户的个人信息仅用于提供更好的服务体验。</text>\n\t\t\t\t\t\t<text>3. 我们不会向第三方泄露用户的个人信息。</text>\n\t\t\t\t\t\t<text>4. 用户有权查看、修改或删除自己的个人信息。</text>\n\t\t\t\t\t\t<text>5. 如有隐私相关问题，请联系我们的客服团队。</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"closePrivacyModal\">我知道了</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { register, getCodeImg, sendSmsCode } from '@/api/user'\n\timport { utils } from '@/api/index'\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 注册表单\n\t\t\t\tregisterForm: {\n\t\t\t\t\tusername: '',\n\t\t\t\t\tphone: '',\n\t\t\t\t\tphoneCode: '',\n\t\t\t\t\tpassword: '',\n\t\t\t\t\tconfirmPassword: '',\n\t\t\t\t\tcode: '',\n\t\t\t\t\tuuid: '',\n\t\t\t\t\tagreement: false\n\t\t\t\t},\n\n\t\t\t\t// 状态控制\n\t\t\t\tregisterLoading: false,\n\t\t\t\tshowPassword: false,\n\t\t\t\tshowConfirmPassword: false,\n\t\t\t\tcaptchaEnabled: true,\n\t\t\t\tcodeUrl: '',\n\n\t\t\t\t// 短信验证码\n\t\t\t\tsmsDisabled: false,\n\t\t\t\tsmsButtonText: '获取验证码',\n\t\t\t\tsmsCountdown: 0,\n\n\t\t\t\t// 弹窗状态\n\t\t\t\tshowAgreementModal: false,\n\t\t\t\tshowPrivacyModal: false\n\t\t\t}\n\t\t},\n\n\t\tcomputed: {\n\t\t\t// 密码强度\n\t\t\tpasswordStrength() {\n\t\t\t\tconst password = this.registerForm.password\n\t\t\t\tif (!password) return 0\n\n\t\t\t\tlet strength = 0\n\t\t\t\t// 长度检查\n\t\t\t\tif (password.length >= 8) strength++\n\t\t\t\t// 包含数字和字母\n\t\t\t\tif (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++\n\t\t\t\t// 包含特殊字符\n\t\t\t\tif (/[^a-zA-Z0-9]/.test(password)) strength++\n\n\t\t\t\treturn strength\n\t\t\t},\n\n\t\t\t// 密码强度文本\n\t\t\tpasswordStrengthText() {\n\t\t\t\tswitch (this.passwordStrength) {\n\t\t\t\t\tcase 0:\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\treturn '弱'\n\t\t\t\t\tcase 2:\n\t\t\t\t\t\treturn '中'\n\t\t\t\t\tcase 3:\n\t\t\t\t\t\treturn '强'\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn ''\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 是否可以注册\n\t\t\tcanRegister() {\n\t\t\t\treturn this.registerForm.username &&\n\t\t\t\t\tthis.registerForm.phone &&\n\t\t\t\t\tthis.registerForm.phoneCode &&\n\t\t\t\t\tthis.registerForm.password &&\n\t\t\t\t\tthis.registerForm.confirmPassword &&\n\t\t\t\t\tthis.registerForm.agreement &&\n\t\t\t\t\t(!this.captchaEnabled || this.registerForm.code)\n\t\t\t}\n\t\t},\n\n\t\tonLoad() {\n\t\t\tthis.initPage()\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 初始化页面\n\t\t\tasync initPage() {\n\t\t\t\t// 获取验证码\n\t\t\t\tawait this.refreshCaptcha()\n\t\t\t},\n\n\t\t\t// 切换密码显示\n\t\t\ttogglePassword() {\n\t\t\t\tthis.showPassword = !this.showPassword\n\t\t\t},\n\n\t\t\t// 切换确认密码显示\n\t\t\ttoggleConfirmPassword() {\n\t\t\t\tthis.showConfirmPassword = !this.showConfirmPassword\n\t\t\t},\n\n\t\t\t// 切换协议同意\n\t\t\ttoggleAgreement() {\n\t\t\t\tthis.registerForm.agreement = !this.registerForm.agreement\n\t\t\t},\n\n\t\t\t// 刷新验证码\n\t\t\tasync refreshCaptcha() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await getCodeImg()\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tthis.captchaEnabled = res.captchaEnabled !== false\n\t\t\t\t\t\tif (this.captchaEnabled) {\n\t\t\t\t\t\t\tthis.codeUrl = \"data:image/gif;base64,\" + res.img\n\t\t\t\t\t\t\tthis.registerForm.uuid = res.uuid\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取验证码失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 发送短信验证码\n\t\t\tasync sendSmsCode() {\n\t\t\t\tif (!this.registerForm.phone) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.registerForm.phone)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await sendSmsCode(this.registerForm.phone)\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '发送成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis.startSmsCountdown()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '发送失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('发送短信失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '发送失败，请稍后再试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 开始短信倒计时\n\t\t\tstartSmsCountdown() {\n\t\t\t\tthis.smsCountdown = 60\n\t\t\t\tthis.smsDisabled = true\n\t\t\t\tthis.smsButtonText = `${this.smsCountdown}s`\n\n\t\t\t\tconst timer = setInterval(() => {\n\t\t\t\t\tthis.smsCountdown--\n\t\t\t\t\tthis.smsButtonText = `${this.smsCountdown}s`\n\n\t\t\t\t\tif (this.smsCountdown <= 0) {\n\t\t\t\t\t\tclearInterval(timer)\n\t\t\t\t\t\tthis.smsDisabled = false\n\t\t\t\t\t\tthis.smsButtonText = '获取验证码'\n\t\t\t\t\t}\n\t\t\t\t}, 1000)\n\t\t\t},\n\n\t\t\t// 处理注册\n\t\t\tasync handleRegister() {\n\t\t\t\t// 表单验证\n\t\t\t\tif (!this.validateForm()) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.registerLoading = true\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await register({\n\t\t\t\t\t\tusername: this.registerForm.username,\n\t\t\t\t\t\tphone: this.registerForm.phone,\n\t\t\t\t\t\tphoneCode: this.registerForm.phoneCode,\n\t\t\t\t\t\tpassword: this.registerForm.password,\n\t\t\t\t\t\tcode: this.registerForm.code,\n\t\t\t\t\t\tuuid: this.registerForm.uuid\n\t\t\t\t\t})\n\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '注册成功',\n\t\t\t\t\t\t\tcontent: '恭喜您注册成功！请前往登录页面登录。',\n\t\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\tthis.goToLogin()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '注册失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\t// 刷新验证码\n\t\t\t\t\t\tif (this.captchaEnabled) {\n\t\t\t\t\t\t\tawait this.refreshCaptcha()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('注册失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '注册失败，请稍后再试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\t// 刷新验证码\n\t\t\t\t\tif (this.captchaEnabled) {\n\t\t\t\t\t\tawait this.refreshCaptcha()\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tthis.registerLoading = false\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 表单验证\n\t\t\tvalidateForm() {\n\t\t\t\tif (!this.registerForm.username) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入用户名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (this.registerForm.username.length < 3) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '用户名至少3个字符',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (!this.registerForm.phone) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.registerForm.phone)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (!this.registerForm.phoneCode) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入短信验证码',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (!this.registerForm.password) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入密码',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (this.registerForm.password.length < 6) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '密码至少6个字符',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (!this.registerForm.confirmPassword) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请确认密码',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (this.registerForm.password !== this.registerForm.confirmPassword) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '两次密码输入不一致',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (this.captchaEnabled && !this.registerForm.code) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入验证码',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tif (!this.registerForm.agreement) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请同意用户协议和隐私政策',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\t// 显示用户协议\n\t\t\tshowAgreement() {\n\t\t\t\tthis.showAgreementModal = true\n\t\t\t},\n\n\t\t\t// 关闭用户协议\n\t\t\tcloseAgreementModal() {\n\t\t\t\tthis.showAgreementModal = false\n\t\t\t},\n\n\t\t\t// 显示隐私政策\n\t\t\tshowPrivacy() {\n\t\t\t\tthis.showPrivacyModal = true\n\t\t\t},\n\n\t\t\t// 关闭隐私政策\n\t\t\tclosePrivacyModal() {\n\t\t\t\tthis.showPrivacyModal = false\n\t\t\t},\n\n\t\t\t// 前往登录页面\n\t\t\tgoToLogin() {\n\t\t\t\tuni.navigateBack()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t/* 注册容器 */\n\t.register-container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 40rpx;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\n\t/* 背景装饰 */\n\t.bg-decoration {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tpointer-events: none;\n\t}\n\n\t.circle {\n\t\tposition: absolute;\n\t\tborder-radius: 50%;\n\t\tbackground: rgba(255, 255, 255, 0.1);\n\t\tanimation: float 6s ease-in-out infinite;\n\t}\n\n\t.circle-1 {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\ttop: 10%;\n\t\tleft: 10%;\n\t\tanimation-delay: 0s;\n\t}\n\n\t.circle-2 {\n\t\twidth: 150rpx;\n\t\theight: 150rpx;\n\t\ttop: 60%;\n\t\tright: 15%;\n\t\tanimation-delay: 2s;\n\t}\n\n\t.circle-3 {\n\t\twidth: 100rpx;\n\t\theight: 100rpx;\n\t\tbottom: 20%;\n\t\tleft: 20%;\n\t\tanimation-delay: 4s;\n\t}\n\n\t@keyframes float {\n\t\t0%, 100% {\n\t\t\ttransform: translateY(0px) rotate(0deg);\n\t\t}\n\t\t50% {\n\t\t\ttransform: translateY(-20px) rotate(180deg);\n\t\t}\n\t}\n\n\t/* 注册卡片 */\n\t.register-card {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tbackdrop-filter: blur(10px);\n\t\tborder-radius: 24rpx;\n\t\tpadding: 60rpx 40rpx;\n\t\twidth: 100%;\n\t\tmax-width: 600rpx;\n\t\tmax-height: 90vh;\n\t\toverflow-y: auto;\n\t\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);\n\t\tposition: relative;\n\t\tz-index: 1;\n\t}\n\n\t.fade-in {\n\t\tanimation: fadeInUp 0.8s ease-out;\n\t}\n\n\t@keyframes fadeInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(50rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t/* 注册头部 */\n\t.register-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 50rpx;\n\t}\n\n\t.logo {\n\t\tfont-size: 80rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.subtitle {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t}\n\n\t/* 表单容器 */\n\t.form-container {\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t/* 输入组 */\n\t.input-group {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 0 20rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tborder: 2rpx solid transparent;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.input-group:focus-within {\n\t\tborder-color: #667eea;\n\t\tbackground: #ffffff;\n\t\tbox-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);\n\t}\n\n\t.input-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 20rpx;\n\t\tcolor: #999999;\n\t}\n\n\t.input-field {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tbackground: transparent;\n\t\tborder: none;\n\t}\n\n\t.input-suffix {\n\t\tfont-size: 32rpx;\n\t\tcolor: #999999;\n\t\tcursor: pointer;\n\t\tpadding: 10rpx;\n\t}\n\n\t/* 验证码相关 */\n\t.captcha-group {\n\t\tpadding-right: 0;\n\t}\n\n\t.captcha-input {\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.captcha-image {\n\t\twidth: 160rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 8rpx;\n\t\toverflow: hidden;\n\t\tcursor: pointer;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: #e9ecef;\n\t}\n\n\t.captcha-img {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.captcha-placeholder {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t}\n\n\t/* 短信验证码 */\n\t.sms-group {\n\t\tpadding-right: 0;\n\t}\n\n\t.sms-input {\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.sms-btn {\n\t\tpadding: 20rpx 30rpx;\n\t\tbackground: #667eea;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 24rpx;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.sms-btn:disabled {\n\t\tbackground: #c0c4cc;\n\t\tcursor: not-allowed;\n\t}\n\n\t/* 密码强度 */\n\t.password-strength {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 30rpx;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.strength-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t\tmargin-right: 15rpx;\n\t}\n\n\t.strength-bar {\n\t\tdisplay: flex;\n\t\tgap: 8rpx;\n\t\tmargin-right: 15rpx;\n\t}\n\n\t.strength-item {\n\t\twidth: 40rpx;\n\t\theight: 8rpx;\n\t\tbackground: #e9ecef;\n\t\tborder-radius: 4rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.strength-item.active:nth-child(1) {\n\t\tbackground: #f56c6c;\n\t}\n\n\t.strength-item.active:nth-child(2) {\n\t\tbackground: #e6a23c;\n\t}\n\n\t.strength-item.active:nth-child(3) {\n\t\tbackground: #67c23a;\n\t}\n\n\t.strength-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n\n\t/* 协议组 */\n\t.agreement-group {\n\t\tdisplay: flex;\n\t\talign-items: flex-start;\n\t\tmargin-bottom: 40rpx;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.checkbox {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tborder: 2rpx solid #ddd;\n\t\tborder-radius: 6rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 15rpx;\n\t\tmargin-top: 4rpx;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\tflex-shrink: 0;\n\t}\n\n\t.checkbox.checked {\n\t\tbackground: #667eea;\n\t\tborder-color: #667eea;\n\t}\n\n\t.checkbox-icon {\n\t\tcolor: #ffffff;\n\t\tfont-size: 20rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.agreement-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666666;\n\t\tline-height: 1.6;\n\t}\n\n\t.agreement-link {\n\t\tcolor: #667eea;\n\t\tcursor: pointer;\n\t}\n\n\t/* 注册按钮 */\n\t.register-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 44rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t.register-btn:hover {\n\t\ttransform: translateY(-2rpx);\n\t\tbox-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);\n\t}\n\n\t.register-btn.loading,\n\t.register-btn:disabled {\n\t\tbackground: #c0c4cc;\n\t\tcursor: not-allowed;\n\t\ttransform: none;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(192, 196, 204, 0.3);\n\t}\n\n\t.loading-icon {\n\t\tmargin-right: 15rpx;\n\t\tanimation: spin 1s linear infinite;\n\t}\n\n\t@keyframes spin {\n\t\tfrom {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\tto {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\n\t/* 底部 */\n\t.register-footer {\n\t\ttext-align: center;\n\t\tmargin-top: 40rpx;\n\t}\n\n\t.footer-link {\n\t\tcolor: #667eea;\n\t\tfont-size: 26rpx;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.footer-link:hover {\n\t\tcolor: #764ba2;\n\t}\n\n\t/* 弹窗样式 */\n\t.modal-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: 1000;\n\t}\n\n\t.modal-content {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\twidth: 90%;\n\t\tmax-width: 600rpx;\n\t\tmax-height: 80vh;\n\t\toverflow-y: auto;\n\t\tbox-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);\n\t\tanimation: modalSlideIn 0.3s ease-out;\n\t}\n\n\t@keyframes modalSlideIn {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(-50rpx) scale(0.9);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0) scale(1);\n\t\t}\n\t}\n\n\t.modal-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\n\t.modal-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.modal-close {\n\t\tfont-size: 48rpx;\n\t\tcolor: #999999;\n\t\tcursor: pointer;\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 50%;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.modal-close:hover {\n\t\tbackground-color: #f5f5f5;\n\t\tcolor: #666666;\n\t}\n\n\t.modal-body {\n\t\tpadding: 30rpx;\n\t}\n\n\t.modal-footer {\n\t\tpadding: 30rpx;\n\t\tborder-top: 1rpx solid #f0f0f0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t}\n\n\t.agreement-content,\n\t.privacy-content {\n\t\tline-height: 1.8;\n\t}\n\n\t.agreement-content text,\n\t.privacy-content text {\n\t\tdisplay: block;\n\t\tmargin-bottom: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.btn {\n\t\tpadding: 20rpx 40rpx;\n\t\tborder-radius: 25rpx;\n\t\tborder: none;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\ttext-align: center;\n\t\tdisplay: inline-block;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.btn-primary {\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t/* 响应式设计 */\n\t@media screen and (max-width: 750rpx) {\n\t\t.register-card {\n\t\t\tpadding: 40rpx 30rpx;\n\t\t}\n\n\t\t.title {\n\t\t\tfont-size: 42rpx;\n\t\t}\n\n\t\t.subtitle {\n\t\t\tfont-size: 26rpx;\n\t\t}\n\n\t\t.agreement-text {\n\t\t\tfont-size: 24rpx;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./register.vue?vue&type=style&index=0&id=891c2434&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755063850479\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}