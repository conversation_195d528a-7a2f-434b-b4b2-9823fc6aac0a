// 简单的Base64和加密解密测试
const { base64Encode, base64Decode } = require('./base64-test.js');

// 测试用例
const testCases = [
    'Hello World',
    'test123',
    '中文测试',
    'password123',
    '!@#$%^&*()',
    'Hello World! 你好世界！',
    ''
];

console.log('=== Base64 编码解码测试 ===');

let allPassed = true;

testCases.forEach((testCase, index) => {
    try {
        console.log(`\n测试 ${index + 1}: "${testCase}"`);

        const encoded = base64Encode(testCase);
        console.log('编码结果:', encoded);

        const decoded = base64Decode(encoded);
        console.log('解码结果:', decoded);

        const passed = testCase === decoded;
        console.log('测试结果:', passed ? '✅ 通过' : '❌ 失败');

        if (!passed) {
            console.error('期望:', testCase);
            console.error('实际:', decoded);
            console.error('长度比较 - 期望:', testCase.length, '实际:', decoded.length);
            allPassed = false;
        }

    } catch (error) {
        console.error(`测试 ${index + 1} 出错:`, error.message);
        allPassed = false;
    }
});

console.log('\n=== 测试总结 ===');
console.log('总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败');

// 测试一些标准的Base64编码
console.log('\n=== 标准Base64对比测试 ===');
const standardTests = [
    { input: 'Hello', expected: 'SGVsbG8=' },
    { input: 'Hello World', expected: 'SGVsbG8gV29ybGQ=' },
    { input: 'test', expected: 'dGVzdA==' }
];

standardTests.forEach((test, index) => {
    try {
        const encoded = base64Encode(test.input);
        const matches = encoded === test.expected;
        console.log(`标准测试 ${index + 1}: "${test.input}"`);
        console.log('我们的结果:', encoded);
        console.log('标准结果:', test.expected);
        console.log('是否匹配:', matches ? '✅' : '❌');

        if (!matches) {
            allPassed = false;
        }
    } catch (error) {
        console.error(`标准测试 ${index + 1} 出错:`, error.message);
        allPassed = false;
    }
});

console.log('\n=== 最终结果 ===');
console.log('所有测试:', allPassed ? '✅ 全部通过' : '❌ 存在问题');
