{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?8648", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?10fd", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?5161", "uni-app:///pages/activation/activation.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?ca6a", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/activation/activation.vue?4d62"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "inputToken", "userQuota", "baseLimit", "activityLimit", "tempLimit", "totalLimit", "currentUseNum", "remainingLimit", "<PERSON><PERSON><PERSON><PERSON>", "newToken", "addTokenFlag", "tokenName", "showTokenDialog", "showDonateDialog", "computed", "todayUsage", "remainingTotalQuota", "onLoad", "methods", "initPage", "tokenApi", "tokenRes", "console", "verifyToken", "uni", "title", "icon", "duration", "res", "tokenTime", "tokenStatus", "tokenCompCode", "detailRes", "tokenDetail", "addToken", "time", "nowTime", "handleTokenDialogClose", "refresh<PERSON><PERSON><PERSON>", "logout", "content", "success", "copyText", "utils", "openDonateDialog", "closeDonateDialog", "goToSignIn", "url", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACqC;;;AAG9F;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4rB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACsNhtB;AAIA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAMAP;cAAA;gBAAAQ;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAJ;;gBAEA;gBACA;kBACAb;kBACAkB;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACA7B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEAY;cAAA;gBAAAY;gBACA;kBACAC;kBACA,kDACA,kBACAA,YACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;cAAA;gBAGAE;kBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAEAH;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAL;gBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBACAC,gCACA;gBACAzB;gBAAA;gBAAA,OAEAS;cAAA;gBAAAQ;gBACA;kBACA;kBACA;;kBAEA;kBACA;;kBAEA;kBACAJ;gBACA;kBACAA;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;gBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAlB;cAAA;gBAAAQ;gBACA;kBACA;kBACAJ;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAL;gBACAE;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MAAA;MACAf;QACAC;QACAe;QACAC;UACA;YACAjB;YACA;YACA;cACAtB;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;YACA;YACA;YAEAgB;cACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAe;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEArB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAsB;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAtB;QACAuB;MACA;IACA;IAEA;IACAC;MACAxB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzeA;AAAA;AAAA;AAAA;AAAygC,CAAgB,m+BAAG,EAAC,C;;;;;;;;;;;ACA7hC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activation/activation.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activation/activation.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./activation.vue?vue&type=template&id=4f5b6586&scoped=true&\"\nvar renderjs\nimport script from \"./activation.vue?vue&type=script&lang=js&\"\nexport * from \"./activation.vue?vue&type=script&lang=js&\"\nimport style0 from \"./activation.vue?vue&type=style&index=0&id=4f5b6586&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f5b6586\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activation/activation.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=template&id=4f5b6586&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 页面头部 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">激活码管理</view>\n\t\t\t<view class=\"header-subtitle\">获取激活码与额度查询</view>\n\t\t</view>\n\n\t\t<!-- 激活码管理卡片 -->\n\t\t<view class=\"card fade-in\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<view class=\"card-title\">我的激活码</view>\n\t\t\t</view>\n\t\t\t<view class=\"card-content\">\n\t\t\t\t<!-- 未登录状态 -->\n\t\t\t\t<view v-if=\"!userInfo\" class=\"token-input-section\">\n\t\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t\t<input v-model=\"inputToken\" placeholder=\"请输入激活码\" class=\"token-input\" maxlength=\"50\" />\n\t\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"verifyToken\">验证激活码</button>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 获取激活码 -->\n\t\t\t\t\t<view v-if=\"!addTokenFlag\" class=\"get-token-section\">\n\t\t\t\t\t\t<view class=\"divider\">或</view>\n\t\t\t\t\t\t<input v-model=\"newToken\" placeholder=\"点击按钮获取激活码\" class=\"token-input\" disabled />\n\t\t\t\t\t\t<button class=\"btn btn-success\" @click=\"addToken\">获取激活码</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 已登录状态 -->\n\t\t\t\t<view v-else class=\"token-info-section\">\n\t\t\t\t\t<!-- 警告提示 -->\n\t\t\t\t\t<view class=\"alert alert-warning\">\n\t\t\t\t\t\t<view class=\"alert-icon\">⚠️</view>\n\t\t\t\t\t\t<view class=\"alert-text\">请妥善保管您的激活码，请勿泄露给他人</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 激活码信息 -->\n\t\t\t\t\t<view class=\"info-list\">\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<view class=\"info-label\">激活码:</view>\n\t\t\t\t\t\t\t<view class=\"info-value-group\">\n\t\t\t\t\t\t\t\t<view class=\"info-value\">{{ userInfo.tokenName }}</view>\n\t\t\t\t\t\t\t\t<view class=\"copy-btn\" @click=\"copyText(userInfo.tokenName)\">📋</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<view class=\"info-label\">绑定设备:</view>\n\t\t\t\t\t\t\t<view class=\"info-value\">{{ userInfo.tokenCompCode || '未绑定设备' }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<view class=\"info-label\">创建时间:</view>\n\t\t\t\t\t\t\t<view class=\"info-value\">{{ userInfo.tokenTime }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"info-item\">\n\t\t\t\t\t\t\t<view class=\"info-label\">账户状态:</view>\n\t\t\t\t\t\t\t<view class=\"tag\" :class=\"userInfo.tokenStatus === 0 ? 'tag-success' : 'tag-danger'\">\n\t\t\t\t\t\t\t\t{{ userInfo.tokenStatus === 0 ? '正常' : '已禁用' }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 额度管理卡片 -->\n\t\t<view v-if=\"userInfo\" class=\"card fade-in\" style=\"animation-delay: 0.1s;\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<view class=\"card-title\">额度使用情况</view>\n\t\t\t</view>\n\t\t\t<view class=\"card-content\">\n\t\t\t\t<!-- 主要额度显示 -->\n\t\t\t\t<view class=\"quota-main\">\n\t\t\t\t\t<view class=\"quota-card today-usage\">\n\t\t\t\t\t\t<view class=\"quota-icon\">📅</view>\n\t\t\t\t\t\t<view class=\"quota-content\">\n\t\t\t\t\t\t\t<view class=\"quota-value\">{{ todayUsage || 0 }}</view>\n\t\t\t\t\t\t\t<view class=\"quota-label\">今日使用额度</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"quota-card remaining-total\">\n\t\t\t\t\t\t<view class=\"quota-icon\">💰</view>\n\t\t\t\t\t\t<view class=\"quota-content\">\n\t\t\t\t\t\t\t<view class=\"quota-value\">{{ remainingTotalQuota || 0 }}</view>\n\t\t\t\t\t\t\t<view class=\"quota-label\">剩余总额度</view>\n\t\t\t\t\t\t\t<view class=\"quota-sublabel\">基础+签到+活动+永久</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 详细额度信息 -->\n\t\t\t\t<view class=\"quota-details\">\n\t\t\t\t\t<view class=\"quota-detail-item\">\n\t\t\t\t\t\t<view class=\"quota-detail-value\">{{ userQuota.baseLimit || 0 }}</view>\n\t\t\t\t\t\t<view class=\"quota-detail-label\">基础额度</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"quota-detail-item\">\n\t\t\t\t\t\t<view class=\"quota-detail-value\">{{ userQuota.activityLimit || 0 }}</view>\n\t\t\t\t\t\t<view class=\"quota-detail-label\">活动额度</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"quota-detail-item\">\n\t\t\t\t\t\t<view class=\"quota-detail-value\">{{ userQuota.tempLimit || 0 }}</view>\n\t\t\t\t\t\t<view class=\"quota-detail-label\">签到额度</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"quota-detail-item\">\n\t\t\t\t\t\t<view class=\"quota-detail-value\">{{ userQuota.permanentQuota || 0 }}</view>\n\t\t\t\t\t\t<view class=\"quota-detail-label\">永久额度</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 永久额度提示 -->\n\t\t\t\t<view class=\"alert alert-warning\">\n\t\t\t\t\t<view class=\"alert-icon\">⚠️</view>\n\t\t\t\t\t<view class=\"alert-text\">永久额度是一次性消耗品，不会刷新重置！永久额度只有在其他额度用完后才会使用！</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 操作按钮 -->\n\t\t\t\t<view class=\"action-buttons\">\n\t\t\t\t\t<button class=\"btn btn-success\" @click=\"refreshQuota\">刷新额度</button>\n\t\t\t\t\t<button class=\"btn btn-danger\" @click=\"logout\">重置激活码</button>\n\t\t\t\t\t<button class=\"btn btn-warning\" @click=\"openDonateDialog\">赞助我们</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 导航按钮 -->\n\t\t<view class=\"nav-buttons\">\n\t\t\t<button class=\"btn btn-primary nav-btn\" @click=\"goToSignIn\">前往签到</button>\n\t\t\t<button class=\"btn btn-secondary nav-btn\" @click=\"goBack\">返回首页</button>\n\t\t</view>\n\n\t\t<!-- 激活码获取成功弹窗 -->\n\t\t<view v-if=\"showTokenDialog\" class=\"modal-overlay\" @click=\"handleTokenDialogClose\">\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"modal-title\">激活码获取成功</view>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"handleTokenDialogClose\">×</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"token-dialog-content\">\n\t\t\t\t\t<view class=\"token-dialog-tip\">\n\t\t\t\t\t\t<text>⚠️ 请妥善保管您的激活码，请勿泄露给他人</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"token-dialog-code\">\n\t\t\t\t\t\t<view class=\"token-dialog-label\">您的激活码:</view>\n\t\t\t\t\t\t<view class=\"token-dialog-value-group\">\n\t\t\t\t\t\t\t<view class=\"token-dialog-value\">{{ newToken }}</view>\n\t\t\t\t\t\t\t<view class=\"copy-btn\" @click=\"copyText(newToken)\">📋</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"token-dialog-steps\">\n\t\t\t\t\t\t<view class=\"step-item completed\">\n\t\t\t\t\t\t\t<view class=\"step-number\">1</view>\n\t\t\t\t\t\t\t<view class=\"step-text\">已获取激活码</view>\n\t\t\t\t\t\t\t<view class=\"step-status\">✅</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"step-item\">\n\t\t\t\t\t\t\t<view class=\"step-number\">2</view>\n\t\t\t\t\t\t\t<view class=\"step-text\">去小说下载器中激活该激活码</view>\n\t\t\t\t\t\t\t<view class=\"step-status\">➡️</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"step-item\">\n\t\t\t\t\t\t\t<view class=\"step-number\">3</view>\n\t\t\t\t\t\t\t<view class=\"step-text\">回到此页面，验证激活码</view>\n\t\t\t\t\t\t\t<view class=\"step-status\">➡️</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"handleTokenDialogClose\">确定</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 赞助弹窗 -->\n\t\t<view v-if=\"showDonateDialog\" class=\"modal-overlay\" @click=\"closeDonateDialog\">\n\t\t\t<view class=\"modal-content\" @click.stop=\"\">\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<view class=\"modal-title\">支持我们的开发</view>\n\t\t\t\t\t<view class=\"modal-close\" @click=\"closeDonateDialog\">×</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"donate-dialog-content\">\n\t\t\t\t\t<view class=\"donate-header\">\n\t\t\t\t\t\t<view class=\"donate-title\">感谢您的支持</view>\n\t\t\t\t\t\t<view class=\"donate-description\">\n\t\t\t\t\t\t\t您的支持是我们持续改进的动力。如果您觉得我们的工具对您有所帮助，可以考虑给予我们一些赞助。\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"donate-methods\">\n\t\t\t\t\t\t<view class=\"donate-method\">\n\t\t\t\t\t\t\t<view class=\"method-title\">支付宝</view>\n\t\t\t\t\t\t\t<view class=\"qr-placeholder\">支付宝收款码</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"donate-method\">\n\t\t\t\t\t\t\t<view class=\"method-title\">微信支付</view>\n\t\t\t\t\t\t\t<view class=\"qr-placeholder\">微信收款码</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"donate-footer\">\n\t\t\t\t\t\t<view class=\"donate-tip\">赞助为自愿行为，不影响软件的正常使用</view>\n\t\t\t\t\t\t<view class=\"donate-tip\">赞助后请留下您的联系方式或者激活码，我们将回馈您一定额度支持</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"btn btn-success\" @click=\"closeDonateDialog\">关闭</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\ttokenApi,\n\t\tutils\n\t} from '@/api/index'\n\timport { checkLogin } from '@/utils/auth'\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 用户信息\n\t\t\t\tuserInfo: null,\n\t\t\t\tinputToken: '',\n\t\t\t\tuserQuota: {\n\t\t\t\t\tbaseLimit: 0,\n\t\t\t\t\tactivityLimit: 0,\n\t\t\t\t\ttempLimit: 0,\n\t\t\t\t\ttotalLimit: 0,\n\t\t\t\t\tcurrentUseNum: 0,\n\t\t\t\t\tremainingLimit: 0,\n\t\t\t\t\tpermanentQuota: 0\n\t\t\t\t},\n\n\t\t\t\t// 激活码相关\n\t\t\t\tnewToken: '',\n\t\t\t\taddTokenFlag: true,\n\t\t\t\ttokenName: '',\n\n\t\t\t\t// 弹窗状态\n\t\t\t\tshowTokenDialog: false,\n\t\t\t\tshowDonateDialog: false\n\t\t\t}\n\t\t},\n\n\t\tcomputed: {\n\t\t\ttodayUsage() {\n\t\t\t\treturn this.userQuota.currentUseNum || 0\n\t\t\t},\n\t\t\tremainingTotalQuota() {\n\t\t\t\tconst totalQuota = (this.userQuota.totalLimit || 0) + (this.userQuota.permanentQuota || 0)\n\t\t\t\tconst used = this.userQuota.currentUseNum || 0\n\t\t\t\treturn Math.max(0, totalQuota - used)\n\t\t\t}\n\t\t},\n\n\t\tonLoad() {\n\t\t\t// 检查登录状态\n\t\t\tif (!checkLogin()) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\tthis.initPage()\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 初始化页面\n\t\t\tasync initPage() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取用户token状态\n\t\t\t\t\tconst tokenRes = await tokenApi.getUserToken()\n\t\t\t\t\tif (tokenRes.code === 200 && tokenRes.data) {\n\t\t\t\t\t\tthis.addTokenFlag = true\n\t\t\t\t\t\tthis.tokenName = tokenRes.data.tokenName\n\t\t\t\t\t\tif (this.tokenName) {\n\t\t\t\t\t\t\tthis.inputToken = this.tokenName\n\t\t\t\t\t\t\tawait this.verifyToken()\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.addTokenFlag = false\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取用户token失败:', error)\n\t\t\t\t\tthis.addTokenFlag = false\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 验证激活码\n\t\t\tasync verifyToken() {\n\t\t\t\tif (!this.inputToken) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入激活码',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\t// 获取激活码信息\n\t\t\t\t\tconst res = await tokenApi.getQuota(this.inputToken)\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\t// 保存激活码到本地存储\n\t\t\t\t\t\tuni.setStorageSync('fq_token', this.inputToken)\n\n\t\t\t\t\t\t// 设置用户信息\n\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\ttokenName: this.inputToken,\n\t\t\t\t\t\t\ttokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),\n\t\t\t\t\t\t\ttokenStatus: 0,\n\t\t\t\t\t\t\ttokenCompCode: '尚未绑定'\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 保存额度信息\n\t\t\t\t\t\tthis.userQuota = res.data || {\n\t\t\t\t\t\t\tbaseLimit: 0,\n\t\t\t\t\t\t\tactivityLimit: 0,\n\t\t\t\t\t\t\ttempLimit: 0,\n\t\t\t\t\t\t\ttotalLimit: 0,\n\t\t\t\t\t\t\tcurrentUseNum: 0,\n\t\t\t\t\t\t\tremainingLimit: 0,\n\t\t\t\t\t\t\tpermanentQuota: 0\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 尝试获取额外的token信息\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst detailRes = await tokenApi.getTokenInfo(this.inputToken)\n\t\t\t\t\t\t\tif (detailRes.code === 200 && detailRes.rows && detailRes.rows.length > 0) {\n\t\t\t\t\t\t\t\tconst tokenDetail = detailRes.rows[0]\n\t\t\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\t\t\t...this.userInfo,\n\t\t\t\t\t\t\t\t\t...tokenDetail\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('获取token详情失败:', error)\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '激活码验证成功',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '激活码无效',\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('验证激活码失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '验证激活码失败，请稍后再试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取激活码\n\t\t\tasync addToken() {\n\t\t\t\ttry {\n\t\t\t\t\tconst time = utils.getCurrentTimeString()\n\t\t\t\t\tconst nowTime = new Date().getTime()\n\t\t\t\t\t// 简化的激活码生成，实际项目中建议使用更安全的方法\n\t\t\t\t\tconst tokenName = utils.encrypt('huswhusbg', 'wqowwjnsm', nowTime.toString())\n\n\t\t\t\t\tconst res = await tokenApi.addToken(tokenName, time)\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tthis.newToken = tokenName\n\t\t\t\t\t\tthis.inputToken = tokenName\n\n\t\t\t\t\t\t// 显示弹窗\n\t\t\t\t\t\tthis.showTokenDialog = true\n\n\t\t\t\t\t\t// 存储激活码\n\t\t\t\t\t\tuni.setStorageSync('fq_token', this.inputToken)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '获取激活码失败',\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取激活码失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取激活码失败，请稍后再试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 激活码弹窗关闭处理\n\t\t\tasync handleTokenDialogClose() {\n\t\t\t\tthis.showTokenDialog = false\n\t\t\t\tthis.addTokenFlag = true\n\t\t\t\tawait this.verifyToken()\n\t\t\t},\n\n\t\t\t// 刷新额度\n\t\t\tasync refreshQuota() {\n\t\t\t\tif (!this.userInfo) return\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await tokenApi.refreshQuota(this.userInfo.tokenName)\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tthis.userQuota = res.data\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '额度刷新成功',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('刷新额度失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '刷新额度失败',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 重置激活码\n\t\t\tlogout() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '确定要重置激活码吗？重置激活码不会重置额度，该按钮旨在帮助遇见bug用户清空当前的激活码，一般情况不要点！',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.removeStorageSync('fq_token')\n\t\t\t\t\t\t\tthis.userInfo = null\n\t\t\t\t\t\t\tthis.userQuota = {\n\t\t\t\t\t\t\t\tbaseLimit: 0,\n\t\t\t\t\t\t\t\tactivityLimit: 0,\n\t\t\t\t\t\t\t\ttempLimit: 0,\n\t\t\t\t\t\t\t\ttotalLimit: 0,\n\t\t\t\t\t\t\t\tcurrentUseNum: 0,\n\t\t\t\t\t\t\t\tremainingLimit: 0,\n\t\t\t\t\t\t\t\tpermanentQuota: 0\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.inputToken = ''\n\t\t\t\t\t\t\tthis.addTokenFlag = false\n\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '已重置激活码',\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 复制文本\n\t\t\tasync copyText(text) {\n\t\t\t\ttry {\n\t\t\t\t\tawait utils.copyText(text)\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('复制失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 打开赞助弹窗\n\t\t\topenDonateDialog() {\n\t\t\t\tthis.showDonateDialog = true\n\t\t\t},\n\n\t\t\t// 关闭赞助弹窗\n\t\t\tcloseDonateDialog() {\n\t\t\t\tthis.showDonateDialog = false\n\t\t\t},\n\n\t\t\t// 前往签到页面\n\t\t\tgoToSignIn() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/signin/signin'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 返回首页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t/* 页面容器 */\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tpadding: 20rpx;\n\t}\n\n\t/* 页面头部 */\n\t.header {\n\t\ttext-align: center;\n\t\tpadding: 40rpx 0;\n\t\tcolor: #ffffff;\n\t}\n\n\t.header-title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 10rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n\t}\n\n\t.header-subtitle {\n\t\tfont-size: 28rpx;\n\t\topacity: 0.9;\n\t}\n\n\t/* 卡片样式 */\n\t.card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\t\tmargin-bottom: 30rpx;\n\t\toverflow: hidden;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.card:hover {\n\t\ttransform: translateY(-4rpx);\n\t\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\n\t}\n\n\t.fade-in {\n\t\tanimation: fadeInUp 0.6s ease-out;\n\t}\n\n\t@keyframes fadeInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(30rpx);\n\t\t}\n\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.card-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\tbackground: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);\n\t}\n\n\t.card-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.card-content {\n\t\tpadding: 30rpx;\n\t}\n\n\t/* 标签样式 */\n\t.tag {\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.tag-success {\n\t\tbackground-color: #f0f9eb;\n\t\tcolor: #67C23A;\n\t\tborder: 1rpx solid #c2e7b0;\n\t}\n\n\t.tag-danger {\n\t\tbackground-color: #fef0f0;\n\t\tcolor: #F56C6C;\n\t\tborder: 1rpx solid #fbc4c4;\n\t}\n\n\t.tag-info {\n\t\tbackground-color: #f4f4f5;\n\t\tcolor: #909399;\n\t\tborder: 1rpx solid #d3d4d6;\n\t}\n\n\t/* 按钮样式 */\n\t.btn {\n\t\tpadding: 20rpx 40rpx;\n\t\tborder-radius: 25rpx;\n\t\tborder: none;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\ttext-align: center;\n\t\tdisplay: inline-block;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.btn:hover {\n\t\ttransform: translateY(-2rpx);\n\t\tbox-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);\n\t}\n\n\t.btn-primary {\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.btn-success {\n\t\tbackground: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.btn-danger {\n\t\tbackground: linear-gradient(135deg, #F56C6C 0%, #f78989 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.btn-warning {\n\t\tbackground: linear-gradient(135deg, #E6A23C 0%, #ebb563 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.btn-secondary {\n\t\tbackground: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.btn-disabled {\n\t\tbackground-color: #c0c4cc;\n\t\tcolor: #ffffff;\n\t\tcursor: not-allowed;\n\t\topacity: 0.6;\n\t}\n\n\t.btn-disabled:hover {\n\t\ttransform: none;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t/* 激活码输入区域 */\n\t.token-input-section {\n\t\tpadding: 20rpx 0;\n\t}\n\n\t.input-group {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 20rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.token-input {\n\t\theight: 80rpx;\n\t\tpadding: 0 20rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 40rpx;\n\t\tfont-size: 28rpx;\n\t\tbackground-color: #ffffff;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.token-input:focus {\n\t\tborder-color: #409EFF;\n\t\tbox-shadow: 0 0 0 4rpx rgba(64, 158, 255, 0.1);\n\t}\n\n\t.get-token-section {\n\t\ttext-align: center;\n\t}\n\n\t.divider {\n\t\tmargin: 30rpx 0;\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tposition: relative;\n\t}\n\n\t.divider::before,\n\t.divider::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\twidth: 30%;\n\t\theight: 1rpx;\n\t\tbackground-color: #e0e0e0;\n\t}\n\n\t.divider::before {\n\t\tleft: 0;\n\t}\n\n\t.divider::after {\n\t\tright: 0;\n\t}\n\n\t/* 激活码信息区域 */\n\t.token-info-section {\n\t\tpadding: 20rpx 0;\n\t}\n\n\t.alert {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 8rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.alert-warning {\n\t\tbackground-color: #fdf6ec;\n\t\tborder: 1rpx solid #f5dab1;\n\t}\n\n\t.alert-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 15rpx;\n\t}\n\n\t.alert-text {\n\t\tflex: 1;\n\t\tfont-size: 26rpx;\n\t\tcolor: #E6A23C;\n\t}\n\n\t.info-list {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.info-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 20rpx 0;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\n\t.info-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.info-label {\n\t\twidth: 150rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t}\n\n\t.info-value {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.info-value-group {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t}\n\n\t.copy-btn {\n\t\tpadding: 10rpx;\n\t\tfont-size: 32rpx;\n\t\tcolor: #409EFF;\n\t\tcursor: pointer;\n\t}\n\n\t/* 额度管理样式 */\n\t.quota-main {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.quota-card {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 30rpx 20rpx;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.quota-card:hover {\n\t\ttransform: translateY(-4rpx);\n\t\tbox-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);\n\t}\n\n\t.today-usage {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.remaining-total {\n\t\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.quota-icon {\n\t\tfont-size: 48rpx;\n\t\tmargin-right: 20rpx;\n\t\topacity: 0.8;\n\t}\n\n\t.quota-content {\n\t\tflex: 1;\n\t}\n\n\t.quota-value {\n\t\tfont-size: 42rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 8rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.quota-label {\n\t\tfont-size: 24rpx;\n\t\topacity: 0.9;\n\t}\n\n\t.quota-sublabel {\n\t\tfont-size: 20rpx;\n\t\topacity: 0.7;\n\t\tmargin-top: 4rpx;\n\t}\n\n\t/* 详细额度信息 */\n\t.quota-details {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(2, 1fr);\n\t\tgap: 20rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.quota-detail-item {\n\t\ttext-align: center;\n\t\tpadding: 25rpx 15rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t\tborder: 1rpx solid #e9ecef;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.quota-detail-item:hover {\n\t\tbackground-color: #e3f2fd;\n\t\tborder-color: #409EFF;\n\t\ttransform: translateY(-2rpx);\n\t}\n\n\t.quota-detail-value {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #409EFF;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.quota-detail-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n\n\t/* 操作按钮 */\n\t.action-buttons {\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\tgap: 15rpx;\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.action-buttons .btn {\n\t\tflex: 1;\n\t\theight: 70rpx;\n\t\tborder-radius: 35rpx;\n\t\tfont-size: 26rpx;\n\t}\n\n\t/* 导航按钮 */\n\t.nav-buttons {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.nav-btn {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder-radius: 40rpx;\n\t\tfont-size: 30rpx;\n\t}\n\n\t/* 弹窗样式 */\n\t.modal-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: 1000;\n\t}\n\n\t.modal-content {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\twidth: 90%;\n\t\tmax-width: 600rpx;\n\t\tmax-height: 80vh;\n\t\toverflow-y: auto;\n\t\tbox-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3);\n\t\tanimation: modalSlideIn 0.3s ease-out;\n\t}\n\n\t@keyframes modalSlideIn {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(-50rpx) scale(0.9);\n\t\t}\n\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0) scale(1);\n\t\t}\n\t}\n\n\t.modal-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\n\t.modal-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.modal-close {\n\t\tfont-size: 48rpx;\n\t\tcolor: #999999;\n\t\tcursor: pointer;\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tborder-radius: 50%;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.modal-close:hover {\n\t\tbackground-color: #f5f5f5;\n\t\tcolor: #666666;\n\t}\n\n\t.modal-footer {\n\t\tpadding: 30rpx;\n\t\tborder-top: 1rpx solid #f0f0f0;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\tgap: 20rpx;\n\t}\n\n\t/* 激活码弹窗样式 */\n\t.token-dialog-content {\n\t\tpadding: 30rpx;\n\t}\n\n\t.token-dialog-tip {\n\t\tbackground-color: #fdf6ec;\n\t\tcolor: #E6A23C;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 8rpx;\n\t\tmargin-bottom: 30rpx;\n\t\ttext-align: center;\n\t\tfont-size: 26rpx;\n\t\tborder-left: 6rpx solid #E6A23C;\n\t}\n\n\t.token-dialog-code {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 30rpx;\n\t\tbackground-color: #f0f9eb;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 8rpx;\n\t\tborder-left: 6rpx solid #67C23A;\n\t}\n\n\t.token-dialog-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tmargin-right: 15rpx;\n\t}\n\n\t.token-dialog-value-group {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 15rpx;\n\t}\n\n\t.token-dialog-value {\n\t\tfont-size: 26rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #67C23A;\n\t\tword-break: break-all;\n\t}\n\n\t.token-dialog-steps {\n\t\tmargin-top: 30rpx;\n\t\ttext-align: left;\n\t\tbackground-color: #f5f7fa;\n\t\tpadding: 30rpx;\n\t\tborder-radius: 12rpx;\n\t}\n\n\t.step-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 20rpx 0;\n\t\tborder-bottom: 1rpx solid #e0e0e0;\n\t}\n\n\t.step-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.step-item.completed {\n\t\tcolor: #67C23A;\n\t}\n\n\t.step-number {\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t\tbackground-color: #409EFF;\n\t\tcolor: #ffffff;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-weight: bold;\n\t\tmargin-right: 20rpx;\n\t\tfont-size: 24rpx;\n\t}\n\n\t.step-item.completed .step-number {\n\t\tbackground-color: #67C23A;\n\t}\n\n\t.step-text {\n\t\tflex: 1;\n\t\tfont-size: 26rpx;\n\t}\n\n\t.step-status {\n\t\tfont-size: 32rpx;\n\t\tmargin-left: 20rpx;\n\t}\n\n\t/* 赞助弹窗样式 */\n\t.donate-dialog-content {\n\t\tpadding: 30rpx 20rpx;\n\t}\n\n\t.donate-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.donate-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.donate-description {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tline-height: 1.6;\n\t\tbackground-color: #f8f8f8;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 8rpx;\n\t}\n\n\t.donate-methods {\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\tmargin: 30rpx 0;\n\t\tgap: 30rpx;\n\t}\n\n\t.donate-method {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t}\n\n\t.method-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.qr-placeholder {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tcolor: #ffffff;\n\t\tborder-radius: 12rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 24rpx;\n\t\tmargin: 0 auto;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.donate-footer {\n\t\tborder-top: 1rpx solid #e0e0e0;\n\t\tpadding-top: 20rpx;\n\t\ttext-align: center;\n\t}\n\n\t.donate-tip {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\tmargin: 10rpx 0;\n\t\tline-height: 1.5;\n\t}\n\n\t/* 响应式设计 */\n\t@media screen and (max-width: 750rpx) {\n\t\t.quota-main {\n\t\t\tflex-direction: column;\n\t\t}\n\n\t\t.quota-details {\n\t\t\tgrid-template-columns: repeat(2, 1fr);\n\t\t}\n\n\t\t.action-buttons {\n\t\t\tflex-direction: column;\n\t\t}\n\n\t\t.donate-methods {\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t}\n\t}\n\n\t@media screen and (max-width: 600rpx) {\n\t\t.quota-details {\n\t\t\tgrid-template-columns: 1fr;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=style&index=0&id=4f5b6586&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./activation.vue?vue&type=style&index=0&id=4f5b6586&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755064655672\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}