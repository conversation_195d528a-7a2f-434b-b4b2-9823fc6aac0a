<template>
	<div class="login_root">
		<div class="login_content">
			<div class="content_left"><el-image :src="loginImg" style="width: 100%;"></el-image></div>
			<div class="content_right">
				<div class="login_form">
					<div style="width: 100%;text-align: left;margin-bottom: 40px;"><span style="font-weight: bolder;font-size: 20px;">欢迎使用本系统</span></div>
					<el-tabs v-model="activeName" @tab-click="handleClick" style="width: 100% !important;">
						<el-tab-pane label="密码登录" name="first" style="width: 100% !important;">
							<el-form :model="ruleForm" :rules="rules" ref="ruleForm"  style="width: 100%;">
								<el-form-item prop="username">
									<el-input v-model="ruleForm.username" placeholder="请输入用户名/手机号">
										<i slot="prefix" class="el-icon-user ico"/>
									</el-input>
								</el-form-item>
								<el-form-item prop="password">
									<el-input v-model="ruleForm.password" type="password" show-password placeholder="请输入密码">
										<i slot="prefix" class="el-icon-lock ico"/>
									</el-input>
								</el-form-item>
								<el-form-item prop="code" v-if="captchaEnabled" style="margin-bottom: 10px !important;">
									<div>
										<el-input v-model="ruleForm.code" placeholder="请输入验证码" style="width: 40%;">
											<i slot="prefix" class="el-icon-circle-check ico"/>
										</el-input>
										<div class="login-code ">
											<img :src="codeUrl" @click="getCode" class="login-code-img"/>
										</div>
									</div>
								</el-form-item>
								<el-checkbox v-model="ruleForm.rememberMe" style="margin:0px 0px 15px 0px;">记住密码</el-checkbox>
								<el-form-item>
									<el-button type="primary" @click="handleLogin" :loading="loading" style="width: 100%;border: none !important;">
										<span v-if="!loading">登 录</span>
										<span v-else>登 录 中...</span>
									</el-button>
									<div style="width: 100%;display: flex;justify-content: space-between;font-size: small;">
										<div>
											<span style="color: #409eff;cursor: pointer;">忘记密码？</span>
										</div>
										<div>
											<span style="color: #999;">没有账号？</span>
											<span style="color: #409eff;cursor: pointer;" @click="openRegister">注册</span>
										</div>
									</div>
								</el-form-item>
								<!--<el-form-item v-if="!register">-->
								<!--	<div>-->
								<!--		&lt;!&ndash;<el-divider><span style="color: #8c8a8a;">更多登录方式</span></el-divider>&ndash;&gt;-->
								<!--		<div>-->
								<!--			<el-button icon="el-icon-refresh" style="width: 100%;border: 1px solid #dedede;border-radius: 20px;" @click.native="openOtherLogin">其他登录方式</el-button>-->
								<!--		</div>-->
								<!--	</div>-->
								<!--</el-form-item>-->
							</el-form>
						</el-tab-pane>
						<el-tab-pane label="手机登录" name="second">
							<el-form :model="otherFrom" ref="otherForm"  :rules="otherRules" style="width: 100%;">
								<el-form-item prop="phone">
									<el-input v-model="otherFrom.phone" placeholder="请输入手机号">
										<i slot="prefix" class="el-icon-mobile-phone ico"/>
									</el-input>
								</el-form-item>
								<el-form-item prop="phoneCode">
									<el-row :gutter="20">
										<el-col :span="14">
											<el-input v-model="otherFrom.phoneCode" placeholder="请输入短信验证码">
												<i slot="prefix" class="el-icon-circle-check ico"/>
											</el-input>
										</el-col>
										<el-col :span="10">
											<el-button type="primary" @click="sendSmsCode" style="width: 100%;border: none !important;">{{captchaBtnStr}}</el-button>
										</el-col>
									</el-row>
								</el-form-item>
								<el-form-item>
									<div>
										<el-button type="primary" :loading="otherLoading" style="width: 100%;border: none !important;" @click.native="otherLogin">
											<span v-if="!otherLoading">手机登录</span>
											<span v-else>手机登录中...</span>
										</el-button>
									</div>
								</el-form-item>
							</el-form>
						</el-tab-pane>
					</el-tabs>
				</div>
			</div>
		</div>
		<el-dialog title="手机登录" :visible.sync="otherLoginFlag" width="50%">
		</el-dialog>
	</div>
</template>

<script>
import loginImg from '../../assets/images/login/login.png'
import {getCodeImg, loginApi, phoneLogin, sendPhoneCode} from "@/api/loginApi";
import Cookies from "js-cookie";
import {decrypt, encrypt} from "@/utils/jsencrypt";
export default {
	name: "login",
	data() {
		return {
			loginImg:loginImg,
			loading: false,
			otherLoading: false,
			ruleForm: {
				username: '',
				password: '',
				code: '',
				uuid:'',
				rememberMe: false
			},
			otherRules:{
				phone: [
					{ required: true, message: '请输入手机号', trigger: 'blur' },
					{ len: 11, message: '请输入正确的手机号', trigger: 'blur' }
				],
				phoneCode: [
					{ required: true, message: '请输入验证码', trigger: 'blur' }
				]
			},
			rules: {
				username: [
					{ required: true, message: '请输入用户名', trigger: 'blur' },
					{ min: 3, message: '长度不少于 3 个字符', trigger: 'blur' }
				],
				password: [
					{ required: true, message: '请输入密码', trigger: 'blur' },
					{ min: 6, message: '长度不少于 6 个字符', trigger: 'blur' }
				],
				phone: [
					{ required: true, message: '请输入手机号', trigger: 'blur' },
					{ len: 11, message: '请输入正确的手机号', trigger: 'blur' }
				],
				phoneCode: [
					{ required: true, message: '请输入验证码', trigger: 'blur' }
				],
				code: [
					{ required: true, message: '请输入验证码', trigger: 'blur'}
				]
			},
			// 验证码开关
			captchaEnabled: true,
			// 注册开关
			register: false,
			codeUrl:'',
			otherFrom:{},
			captchaBtnStr: "获取验证码",
			otherLoginFlag: false,
			activeName:'first',
		}
	},
	created() {
		// 获取验证码
		this.getCode();
		// 获取记得密码
		this.getCookie();
	},
	mounted() {

	},
	methods: {
		getCookie() {
			// const username = Cookies.get("username");
			// const password = Cookies.get("password");
			// const rememberMe = Cookies.get('rememberMe')
			let username = localStorage.getItem("username");
			let password = localStorage.getItem("password");
			let rememberMe = localStorage.getItem('rememberMe')
			// console.log("===="+username,password,rememberMe)
			rememberMe = rememberMe === 'true'
			this.ruleForm = {
				username: username ===  null ? this.ruleForm.username : username,
				password: password === null ? this.ruleForm.password : decrypt(password),
				rememberMe: rememberMe
			};
		},
		handleClick(){
			if(this.activeName === "first"){
				// 清除校验结果
				this.$refs.ruleForm.clearValidate();
			}else if(this.activeName === "second"){
				this.$refs.otherForm.clearValidate();
			}
		},
		// 打开并清除窗口数据
		openOtherLogin(){
			this.otherLoginFlag = true;
			this.$refs.otherForm.clearValidate();
			this.otherFrom = {}
			this.otherLoading = false
			// this.$confirm('确定关闭？', '提示', {
			// 	confirmButtonText: '确定',
			// 	cancelButtonText: '取消',
			// 	type: 'warning'
			// }).then(() => {
			//
			// }).catch(() => {
			// 	this.otherLoginFlag = true;
			// 	this.$message({
			// 		type: 'info',
			// 		message: '已取消关闭'
			// 	});
			// })
		},
		//其他方法登录
		otherLogin(){
			if(this.otherFrom.phone === "" || this.otherFrom.phone === undefined){
				this.$message.error("请填写手机号！");
				return
			}
			if(this.otherFrom.phoneCode === "" || this.otherFrom.phoneCode === undefined){
				this.$message.error("请填写短信验证码！");
				return
			}
			this.$refs.otherForm.validate(valid => {
				if (valid) {
					this.otherLoading = true;
					phoneLogin(this.otherFrom.phone, this.otherFrom.phoneCode).then(res => {
						if(res.code === 200){
							this.otherLoading = false;
							this.$message.success('登录成功');
							localStorage.setItem("Authorization", res.token);
							this.$router.push('/')
						} else {
							this.$message.error(res.msg);
							this.otherLoading = false;
						}
					})
				}
			})
		},
		// 发送短信验证码
		sendSmsCode() {
			if(this.otherFrom.phone !== "" && this.otherFrom.phone !== undefined) {
				sendPhoneCode(this.otherFrom.phone).then(res => {
					if(res.msg === "发送成功") {
						this.$message.success("发送成功");
						this.captchaBtnDisable = true;
						let timer = 60, minutes, seconds;
						const interval = setInterval(() => {
							minutes = Math.floor(timer / 60);
							seconds = timer % 60;
							seconds = seconds < 10 ? '0' + seconds : seconds;
							this.captchaBtnStr = `${seconds} 秒后重新发送`;

							if (--timer < 0) {
								clearInterval(interval);
								this.captchaBtnStr = "获取验证码";
								this.captchaBtnDisable = false;
							}
						}, 1000);
					} else {
						this.$message.error(res.msg);
					}
				});
			} else {
				this.$message.error("请填写手机号！");
			}
		},
		handleLogin() {
			this.$refs.ruleForm.validate(valid => {
				if (valid) {
					//记住密码
					if (this.ruleForm.rememberMe) {
						// Cookies.set("username", this.ruleForm.username, { expires: 30 });
						// Cookies.set("password", encrypt(this.ruleForm.password), { expires: 30 });
						// Cookies.set('rememberMe', this.ruleForm.rememberMe, { expires: 30 });
						localStorage.setItem("username", this.ruleForm.username);
						localStorage.setItem("password", encrypt(this.ruleForm.password));
						localStorage.setItem('rememberMe', this.ruleForm.rememberMe);
					} else {
						// Cookies.remove("username");
						// Cookies.remove("password");
						// Cookies.remove('rememberMe');
						localStorage.removeItem("username");
						localStorage.removeItem("password");
						localStorage.removeItem('rememberMe');
					}
					this.loading = true;
					loginApi(this.ruleForm.username, this.ruleForm.password, this.ruleForm.code, this.ruleForm.uuid).then(res => {
						if (res.code === 200) {
							this.$message.success('登录成功');
							localStorage.setItem("Authorization", res.token);
							this.$router.push('/')

							// localStorage.setItem("token", res.data.token);
							// localStorage.setItem("username", res.data.username);
							// localStorage.setItem("avatar", res.data.avatar);
							// localStorage.setItem("roles", res.data.roles);
							// localStorage.setItem("userId", res.data.userId);
							// localStorage.setItem("userInfo", JSON.stringify(res.data));
						}else {
							this.loading = false;
						}
					}).catch(() => {
						this.loading = false;
						if (this.captchaEnabled) {
							this.getCode();
						}
					});
				}
			});
		},
		openRegister(){
			// 清除校验结果
			this.$refs.ruleForm.clearValidate();
			this.$router.push({path: '/register'});
		},
		getCode() {
			getCodeImg().then(res => {
				this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
				if (this.captchaEnabled) {
					this.codeUrl = "data:image/gif;base64," + res.img;
					this.ruleForm.uuid = res.uuid;
				}
			});
		},
	}
}
</script>

<style scoped>
.login_root{
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	background: #fff;
	/*border: 1px solid red;*/
}
.login_content{
	width: 70vw;
	height: 70vh;
	/*border: 1px solid #000;*/
	display: flex;
}
.content_left{
	width: 50%;
	display: flex;
	align-items: center;
	/*border: 1px solid #000;*/
}

.content_right{
	width: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	/*border: 1px solid #000;*/
}

.login_form{
	width: 80%;
	padding: 30px;
	border: 1px solid #dedede;
	box-sizing: border-box;
	border-radius: 10px;
	display: flex;
	flex-direction: column;
	align-items: start;
	box-shadow: 0 2px 12px 0 rgb(0 0 0 / 20%);
}

.ico{
	font-size: 18px;
	margin-top: 10px;
}

.login-code-img {
	height: 38px;
}

.login-code {
	width: 50%;
	height: 38px;
	float: right;
}

.login-code img {
	cursor: pointer;
	vertical-align: middle;
}
</style>
