// 模拟 API 响应 - 仅用于演示和开发测试
// 在生产环境中请替换为真实的 API 调用

// 模拟数据存储
const mockStorage = {
	tokens: new Map(),
	signInRecords: new Map(),
	userTokens: new Map()
}

// 生成模拟的激活码
function generateMockToken() {
	const timestamp = Date.now()
	const random = Math.random().toString(36).substring(2, 15)
	return `mock_${timestamp}_${random}`
}

// 模拟 API 响应
export const mockApi = {
	// 模拟获取用户token状态
	async getUserToken() {
		await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟

		const storedToken = uni.getStorageSync('fq_token')
		if (storedToken) {
			return {
				code: 200,
				data: {
					tokenName: storedToken
				},
				msg: '获取成功'
			}
		}

		return {
			code: 404,
			data: null,
			msg: '未找到token'
		}
	},

	// 模拟获取激活码额度信息
	async getQuota(tokenName) {
		await new Promise(resolve => setTimeout(resolve, 500))

		if (!tokenName) {
			return {
				code: 400,
				data: null,
				msg: '激活码不能为空'
			}
		}

		// 模拟额度数据
		const mockQuota = {
			baseLimit: 100,
			activityLimit: 50,
			tempLimit: 30,
			totalLimit: 180,
			currentUseNum: 25,
			remainingLimit: 155,
			permanentQuota: 20
		}

		return {
			code: 200,
			data: mockQuota,
			msg: '获取成功'
		}
	},

	// 模拟获取激活码详细信息
	async getTokenInfo(tokenName) {
		await new Promise(resolve => setTimeout(resolve, 500))

		const mockTokenInfo = {
			tokenName: tokenName,
			tokenTime: new Date().toISOString().split('T')[0],
			tokenStatus: 0,
			tokenCompCode: '未绑定设备'
		}

		return {
			code: 200,
			rows: [mockTokenInfo],
			total: 1,
			msg: '获取成功'
		}
	},

	// 模拟新增激活码
	async addToken(tokenName, tokenTime) {
		await new Promise(resolve => setTimeout(resolve, 800))

		// 存储到模拟存储中
		mockStorage.tokens.set(tokenName, {
			tokenName,
			tokenTime,
			status: 0
		})

		return {
			code: 200,
			data: {
				tokenName,
				tokenTime
			},
			msg: '激活码创建成功'
		}
	},

	// 模拟获取签到配置
	async getSignInSetting() {
		await new Promise(resolve => setTimeout(resolve, 300))

		return {
			code: 200,
			data: {
				dailySignInReward: 5,
				enableContinuousReward: true,
				continuousDaysRequired: 7,
				continuousReward: 15
			},
			msg: '获取成功'
		}
	},

	// 模拟检查今日签到状态
	async checkSignInToday(tokenName) {
		await new Promise(resolve => setTimeout(resolve, 300))

		const today = new Date().toDateString()
		const key = `${tokenName}_${today}`
		const hasSigned = mockStorage.signInRecords.has(key)

		return {
			code: 200,
			data: hasSigned,
			msg: '检查成功'
		}
	},

	// 模拟执行签到
	async doSignIn(tokenName) {
		await new Promise(resolve => setTimeout(resolve, 600))

		const today = new Date().toDateString()
		const key = `${tokenName}_${today}`

		if (mockStorage.signInRecords.has(key)) {
			return {
				code: 400,
				data: null,
				msg: '今日已签到'
			}
		}

		// 记录签到
		mockStorage.signInRecords.set(key, {
			tokenName,
			signInTime: new Date().toISOString(),
			rewardAmount: 5,
			extraReward: false
		})

		return {
			code: 200,
			data: {
				rewardAmount: 5,
				extraReward: false
			},
			msg: '签到成功'
		}
	},

	// 模拟获取签到历史
	async getSignInHistory(tokenName, pageNum = 1, pageSize = 10) {
		await new Promise(resolve => setTimeout(resolve, 400))

		// 生成模拟的签到历史数据
		const mockHistory = []
		const today = new Date()

		for (let i = 0; i < 15; i++) {
			const date = new Date(today)
			date.setDate(today.getDate() - i)

			// 模拟不是每天都签到
			if (Math.random() > 0.3) {
				mockHistory.push({
					id: `sign_${i}`,
					tokenName,
					signInTime: date.toISOString(),
					rewardAmount: i % 7 === 6 ? 20 : 5, // 每7天一个连续奖励
					extraReward: i % 7 === 6
				})
			}
		}

		// 分页处理
		const start = (pageNum - 1) * pageSize
		const end = start + pageSize
		const paginatedData = mockHistory.slice(start, end)

		return {
			code: 200,
			rows: paginatedData,
			total: mockHistory.length,
			msg: '获取成功'
		}
	},

	// 模拟用户登录
	async login(data) {
		await new Promise(resolve => setTimeout(resolve, 800))

		const { username, password, code, uuid } = data

		// 简单的模拟验证
		if (!username || !password) {
			return {
				code: 400,
				data: null,
				msg: '用户名和密码不能为空'
			}
		}

		// 模拟验证码检查
		if (code && code !== '1234') {
			return {
				code: 400,
				data: null,
				msg: '验证码错误'
			}
		}

		// 模拟登录成功
		const mockToken = `mock_token_${Date.now()}_${Math.random().toString(36).substring(2)}`

		// 存储用户信息到本地
		uni.setStorageSync('user_token', mockToken)
		uni.setStorageSync('user_info', {
			username,
			userId: 'mock_user_' + Date.now(),
			avatar: '',
			roles: ['user']
		})

		return {
			code: 200,
			data: {
				token: mockToken,
				username,
				userId: 'mock_user_' + Date.now(),
				avatar: '',
				roles: ['user']
			},
			token: mockToken,
			msg: '登录成功'
		}
	},

	// 模拟用户注册
	async register(data) {
		await new Promise(resolve => setTimeout(resolve, 1000))

		const { username, password, phone, phoneCode, code } = data

		// 简单的模拟验证
		if (!username || !password || !phone) {
			return {
				code: 400,
				data: null,
				msg: '用户名、密码和手机号不能为空'
			}
		}

		// 模拟验证码检查
		if (code && code !== '1234') {
			return {
				code: 400,
				data: null,
				msg: '图形验证码错误'
			}
		}

		if (phoneCode && phoneCode !== '123456') {
			return {
				code: 400,
				data: null,
				msg: '短信验证码错误'
			}
		}

		// 模拟注册成功
		return {
			code: 200,
			data: {
				username,
				userId: 'mock_user_' + Date.now()
			},
			msg: '注册成功'
		}
	},

	// 模拟获取用户信息
	async getUserInfo() {
		await new Promise(resolve => setTimeout(resolve, 300))

		const userInfo = uni.getStorageSync('user_info')
		if (userInfo) {
			return {
				code: 200,
				data: userInfo,
				msg: '获取成功'
			}
		}

		return {
			code: 401,
			data: null,
			msg: '用户未登录'
		}
	},

	// 模拟用户退出
	async logout() {
		await new Promise(resolve => setTimeout(resolve, 300))

		// 清除本地存储
		uni.removeStorageSync('user_token')
		uni.removeStorageSync('user_info')

		return {
			code: 200,
			data: null,
			msg: '退出成功'
		}
	},

	// 模拟获取验证码
	async getCaptcha() {
		await new Promise(resolve => setTimeout(resolve, 500))

		// 生成模拟的验证码图片（base64）
		const mockCaptcha = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='

		return {
			code: 200,
			data: {
				img: mockCaptcha,
				uuid: 'mock_uuid_' + Date.now(),
				captchaEnabled: true
			},
			captchaEnabled: true,
			img: mockCaptcha,
			uuid: 'mock_uuid_' + Date.now(),
			msg: '获取成功'
		}
	},

	// 模拟获取登录配置
	async getLoginInfo() {
		await new Promise(resolve => setTimeout(resolve, 300))

		return {
			code: 200,
			data: {
				captchaEnabled: true,
				registerEnabled: true
			},
			msg: '获取成功'
		}
	},

	// 模拟发送短信验证码
	async sendSmsCode(phone) {
		await new Promise(resolve => setTimeout(resolve, 600))

		if (!phone || phone.length !== 11) {
			return {
				code: 400,
				data: null,
				msg: '请输入正确的手机号'
			}
		}

		return {
			code: 200,
			data: null,
			msg: '发送成功'
		}
	},

	// 模拟发送手机登录验证码
	async sendPhoneCode(phone) {
		await new Promise(resolve => setTimeout(resolve, 600))

		if (!phone || phone.length !== 11) {
			return {
				code: 400,
				data: null,
				msg: '请输入正确的手机号'
			}
		}

		return {
			code: 200,
			data: null,
			msg: '发送成功'
		}
	},

	// 模拟手机验证码登录
	async phoneLogin(phone, code) {
		await new Promise(resolve => setTimeout(resolve, 800))

		if (!phone || !code) {
			return {
				code: 400,
				data: null,
				msg: '手机号和验证码不能为空'
			}
		}

		if (code !== '123456') {
			return {
				code: 400,
				data: null,
				msg: '验证码错误'
			}
		}

		// 模拟登录成功
		const mockToken = `mock_token_${Date.now()}_${Math.random().toString(36).substring(2)}`

		// 存储用户信息到本地
		uni.setStorageSync('user_token', mockToken)
		uni.setStorageSync('user_info', {
			username: phone,
			userId: 'mock_user_' + Date.now(),
			avatar: '',
			roles: ['user']
		})

		return {
			code: 200,
			data: {
				token: mockToken,
				username: phone,
				userId: 'mock_user_' + Date.now()
			},
			token: mockToken,
			msg: '登录成功'
		}
	}
}

// 检查是否应该使用模拟 API
export function shouldUseMockApi() {
	// 如果 API 地址还是占位符，则使用模拟 API
	try {
		const config = require('@/config/index').default
		return config.baseURL.includes('your-api-domain.com')
	} catch (error) {
		return true
	}
}
