// 测试不同的签到API调用方式
import http from '@/utils/request'

// 测试不同的API调用方式
export async function testDifferentApiMethods(tokenName) {
    console.log('=== 测试不同的签到API调用方式 ===')
    console.log('测试tokenName:', tokenName)
    
    const methods = [
        {
            name: '方法1: JSON格式请求体',
            call: () => http.post('/sign_in/do_sign_in', {
                tokenName: tokenName
            })
        },
        {
            name: '方法2: URL参数',
            call: () => http.post('/sign_in/do_sign_in', {}, {
                params: { tokenName }
            })
        },
        {
            name: '方法3: Form-data格式',
            call: () => http.post('/sign_in/do_sign_in', {
                tokenName: tokenName
            }, {
                header: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            })
        },
        {
            name: '方法4: 直接URL拼接',
            call: () => http.post(`/sign_in/do_sign_in?tokenName=${encodeURIComponent(tokenName)}`, {})
        },
        {
            name: '方法5: GET请求',
            call: () => http.get('/sign_in/do_sign_in', { tokenName })
        }
    ]
    
    const results = []
    
    for (const method of methods) {
        try {
            console.log(`\n测试: ${method.name}`)
            const result = await method.call()
            console.log(`${method.name} - 成功:`, result)
            results.push({
                method: method.name,
                success: true,
                result: result
            })
        } catch (error) {
            console.log(`${method.name} - 失败:`, error.message)
            results.push({
                method: method.name,
                success: false,
                error: error.message
            })
        }
    }
    
    console.log('\n=== 测试结果汇总 ===')
    results.forEach(result => {
        console.log(`${result.method}: ${result.success ? '✅ 成功' : '❌ 失败'}`)
        if (!result.success) {
            console.log(`  错误: ${result.error}`)
        }
    })
    
    return results
}

// 模拟签到API调用测试
export function simulateSignInApiCall(tokenName) {
    console.log('\n=== 模拟签到API调用 ===')
    console.log('tokenName:', tokenName)
    
    // 检查参数
    if (!tokenName) {
        console.log('❌ tokenName为空')
        return false
    }
    
    if (typeof tokenName !== 'string') {
        console.log('❌ tokenName不是字符串类型')
        return false
    }
    
    if (tokenName.trim() === '') {
        console.log('❌ tokenName为空字符串')
        return false
    }
    
    console.log('✅ tokenName验证通过')
    
    // 模拟不同的请求格式
    const requestFormats = [
        {
            name: 'JSON请求体',
            data: { tokenName: tokenName },
            contentType: 'application/json'
        },
        {
            name: 'Form-data请求体',
            data: `tokenName=${encodeURIComponent(tokenName)}`,
            contentType: 'application/x-www-form-urlencoded'
        },
        {
            name: 'URL参数',
            url: `/sign_in/do_sign_in?tokenName=${encodeURIComponent(tokenName)}`,
            data: {},
            contentType: 'application/json'
        }
    ]
    
    console.log('\n可能的请求格式:')
    requestFormats.forEach((format, index) => {
        console.log(`${index + 1}. ${format.name}:`)
        console.log(`   Content-Type: ${format.contentType}`)
        if (format.url) {
            console.log(`   URL: ${format.url}`)
        }
        console.log(`   Data:`, format.data)
    })
    
    return true
}

// 检查当前API配置
export function checkCurrentApiConfig() {
    console.log('\n=== 检查当前API配置 ===')
    
    try {
        // 检查signInApi是否正确导入
        const { signInApi } = require('@/api/index')
        
        if (!signInApi) {
            console.log('❌ signInApi未正确导入')
            return false
        }
        
        if (typeof signInApi.doSignIn !== 'function') {
            console.log('❌ doSignIn方法不存在')
            return false
        }
        
        console.log('✅ signInApi配置正确')
        
        // 检查方法定义
        console.log('doSignIn方法:', signInApi.doSignIn.toString())
        
        return true
    } catch (error) {
        console.error('❌ 检查API配置失败:', error)
        return false
    }
}

// 运行完整测试
export async function runCompleteSignInApiTest() {
    console.log('开始完整的签到API测试...\n')
    
    const testTokenName = '51938f2de51acf931d5d62e7f96ed476' // 使用实际的tokenName
    
    // 1. 检查API配置
    const configCheck = checkCurrentApiConfig()
    
    // 2. 模拟API调用
    const simulationCheck = simulateSignInApiCall(testTokenName)
    
    // 3. 实际测试不同方法（如果API服务器可用）
    let apiTestResults = null
    try {
        console.log('\n尝试实际API调用测试...')
        apiTestResults = await testDifferentApiMethods(testTokenName)
    } catch (error) {
        console.log('实际API测试跳过（API服务器不可用）:', error.message)
    }
    
    console.log('\n=== 完整测试结果 ===')
    console.log('API配置检查:', configCheck ? '✅ 通过' : '❌ 失败')
    console.log('模拟调用检查:', simulationCheck ? '✅ 通过' : '❌ 失败')
    
    if (apiTestResults) {
        const successfulMethods = apiTestResults.filter(r => r.success)
        console.log(`实际API测试: ${successfulMethods.length}/${apiTestResults.length} 个方法成功`)
        
        if (successfulMethods.length > 0) {
            console.log('成功的方法:')
            successfulMethods.forEach(method => {
                console.log(`  - ${method.method}`)
            })
        }
    }
    
    const overallSuccess = configCheck && simulationCheck
    console.log('总体结果:', overallSuccess ? '✅ 基础配置正确' : '❌ 存在配置问题')
    
    if (overallSuccess) {
        console.log('\n💡 建议:')
        console.log('1. 当前使用Form-data格式，这是最常见的后端期望格式')
        console.log('2. 如果仍然失败，请检查后端API文档确认参数格式')
        console.log('3. 可以尝试联系后端开发者确认API接口规范')
    }
    
    return {
        configCheck,
        simulationCheck,
        apiTestResults,
        overall: overallSuccess
    }
}
