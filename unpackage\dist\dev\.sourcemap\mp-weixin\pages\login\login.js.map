{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?e037", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?3ca0", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?78d2", "uni-app:///pages/login/login.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?c2ce", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/login/login.vue?6ea9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "activeTab", "passwordForm", "username", "password", "code", "uuid", "rememberMe", "phoneForm", "phone", "phoneCode", "passwordLoading", "phoneLoading", "showPassword", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "codeUrl", "smsDisabled", "smsButtonText", "smsCountdown", "onLoad", "methods", "initPage", "switchTab", "togglePassword", "toggleRemember", "refreshCaptcha", "res", "console", "loadRememberedPassword", "saveRememberedPassword", "uni", "handlePasswordLogin", "title", "icon", "token", "userInfo", "userId", "avatar", "roles", "savedToken", "savedUserInfo", "duration", "setTimeout", "url", "success", "fail", "sendSmsCode", "startSmsCountdown", "clearInterval", "handlePhoneLogin", "goToRegister"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAurB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACwG3sB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACA;QACA;QAEA;UACA;UACA;UACA;YACA;UACA;YACAD;YACA;UACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAE;MACA;QACA;UACAC;UACA;UACA;YACA;YACAA;YACAH;UACA;YACAA;YACA;YACAG;UACA;UACAA;QACA;UACAA;UACAA;UACAA;QACA;MACA;QACAH;QACA;MACA;IACA;IAEA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAD;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGA,oBACA,8BACA,8BACA,0BACA,yBACA;cAAA;gBALAP;gBAAA,MAOAA;kBAAA;kBAAA;gBAAA;gBACAC;;gBAEA;gBAAA;gBAEAO;gBACAP;gBACAA;;gBAEA;gBACA;;gBAEA;gBACAQ;kBACAhC;kBACAiC;kBACAC;kBACAC;kBACA7B;gBACA;gBACAkB;gBACA;gBACAA;;gBAEA;gBACAY;gBACAC;gBACAb;gBACAA;gBAAA,IAEAY;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAEAC;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAb;gBACAG;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBACA;kBACA;gBACA;kBACAN;gBACA;gBAEAG;kBACAE;kBACAC;kBACAQ;gBACA;;gBAEA;gBACAC;kBACA;kBACA;kBACA;kBAEAf;kBACAA;kBACAA;kBAEA;oBACAA;oBACAG;sBACAE;sBACAC;oBACA;oBACA;kBACA;kBAEAN;kBACA;kBACA;kBAEAG;oBACAa;oBACAC;sBACAjB;oBACA;oBACAkB;sBACAlB;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAA;gBACAG;kBACAE;kBACAC;gBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAN;gBACAG;kBACAE;kBACAC;gBACA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAGA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAhB;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAP;gBACA;kBACAI;oBACAE;oBACAC;kBACA;kBACA;gBACA;kBACAH;oBACAE;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAN;gBACAG;kBACAE;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAc;MAAA;MACA;MACA;MACA;MAEA;QACA;QACA;QAEA;UACAC;UACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAnB;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAIA;kBAAA;kBAAA;gBAAA;gBACAH;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAP;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAGAQ;gBACAP;gBACAA;;gBAEA;gBACA;;gBAEA;gBACAQ;kBACAhC;kBACAiC;kBACAC;kBACAC;kBACA7B;gBACA;gBACAkB;gBACA;gBACAA;;gBAEA;gBACAY;gBACAC;gBACAb;gBACAA;gBAAA,IAEAY;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA,IAEAC;kBAAA;kBAAA;gBAAA;gBAAA,MACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAb;gBACAG;kBACAE;kBACAC;gBACA;gBAAA;cAAA;gBAIAH;kBACAE;kBACAC;kBACAQ;gBACA;;gBAEA;gBACAC;kBACA;kBACA;kBACA;kBAEAf;kBACAA;kBACAA;kBAEA;oBACAA;oBACAG;sBACAE;sBACAC;oBACA;oBACA;kBACA;kBAEAN;kBACA;kBACA;kBAEAG;oBACAa;oBACAC;sBACAjB;oBACA;oBACAkB;sBACAlB;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAG;kBACAE;kBACAC;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAN;gBACAG;kBACAE;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAiB;MACApB;QACAa;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1kBA;AAAA;AAAA;AAAA;AAAogC,CAAgB,89BAAG,EAAC,C;;;;;;;;;;;ACAxhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b237504c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=template&id=b237504c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"login-container\">\n\t\t<!-- 背景装饰 -->\n\t\t<view class=\"bg-decoration\">\n\t\t\t<view class=\"circle circle-1\"></view>\n\t\t\t<view class=\"circle circle-2\"></view>\n\t\t\t<view class=\"circle circle-3\"></view>\n\t\t</view>\n\n\t\t<!-- 登录卡片 -->\n\t\t<view class=\"login-card fade-in\">\n\t\t\t<!-- 头部 -->\n\t\t\t<view class=\"login-header\">\n\t\t\t\t<view class=\"logo\">🔐</view>\n\t\t\t\t<view class=\"title\">欢迎使用本系统</view>\n\t\t\t\t<view class=\"subtitle\">请选择登录方式</view>\n\t\t\t</view>\n\n\t\t\t<!-- 标签页切换 -->\n\t\t\t<view class=\"tab-container\">\n\t\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'password' }\" @click=\"switchTab('password')\">\n\t\t\t\t\t密码登录\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'phone' }\" @click=\"switchTab('phone')\">\n\t\t\t\t\t手机登录\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 密码登录表单 -->\n\t\t\t<view v-if=\"activeTab === 'password'\" class=\"form-container\">\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<view class=\"input-icon\">👤</view>\n\t\t\t\t\t<input v-model=\"passwordForm.username\" placeholder=\"请输入用户名/手机号\" class=\"input-field\" />\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<view class=\"input-icon\">🔒</view>\n\t\t\t\t\t<input v-model=\"passwordForm.password\" placeholder=\"请输入密码\" class=\"input-field\" \n\t\t\t\t\t\t:password=\"!showPassword\" />\n\t\t\t\t\t<view class=\"input-suffix\" @click=\"togglePassword\">\n\t\t\t\t\t\t{{ showPassword ? '🙈' : '👁️' }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 验证码 -->\n\t\t\t\t<view v-if=\"captchaEnabled\" class=\"input-group captcha-group\">\n\t\t\t\t\t<view class=\"input-icon\">🔢</view>\n\t\t\t\t\t<input v-model=\"passwordForm.code\" placeholder=\"请输入验证码\" class=\"input-field captcha-input\" />\n\t\t\t\t\t<view class=\"captcha-image\" @click=\"refreshCaptcha\">\n\t\t\t\t\t\t<image v-if=\"codeUrl\" :src=\"codeUrl\" class=\"captcha-img\" mode=\"aspectFit\" />\n\t\t\t\t\t\t<view v-else class=\"captcha-placeholder\">点击获取</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 记住密码 -->\n\t\t\t\t<view class=\"checkbox-group\">\n\t\t\t\t\t<view class=\"checkbox\" :class=\"{ checked: passwordForm.rememberMe }\" @click=\"toggleRemember\">\n\t\t\t\t\t\t<view class=\"checkbox-icon\">{{ passwordForm.rememberMe ? '✓' : '' }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"checkbox-label\">记住密码</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 登录按钮 -->\n\t\t\t\t<button class=\"login-btn\" :class=\"{ loading: passwordLoading }\" :disabled=\"passwordLoading\" \n\t\t\t\t\t@click=\"handlePasswordLogin\">\n\t\t\t\t\t<view v-if=\"passwordLoading\" class=\"loading-icon\">⏳</view>\n\t\t\t\t\t{{ passwordLoading ? '登录中...' : '登录' }}\n\t\t\t\t</button>\n\t\t\t</view>\n\n\t\t\t<!-- 手机登录表单 -->\n\t\t\t<view v-if=\"activeTab === 'phone'\" class=\"form-container\">\n\t\t\t\t<view class=\"input-group\">\n\t\t\t\t\t<view class=\"input-icon\">📱</view>\n\t\t\t\t\t<input v-model=\"phoneForm.phone\" placeholder=\"请输入手机号\" class=\"input-field\" type=\"number\" />\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"input-group sms-group\">\n\t\t\t\t\t<view class=\"input-icon\">💬</view>\n\t\t\t\t\t<input v-model=\"phoneForm.phoneCode\" placeholder=\"请输入短信验证码\" class=\"input-field sms-input\" />\n\t\t\t\t\t<button class=\"sms-btn\" :disabled=\"smsDisabled\" @click=\"sendSmsCode\">\n\t\t\t\t\t\t{{ smsButtonText }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 登录按钮 -->\n\t\t\t\t<button class=\"login-btn\" :class=\"{ loading: phoneLoading }\" :disabled=\"phoneLoading\" \n\t\t\t\t\t@click=\"handlePhoneLogin\">\n\t\t\t\t\t<view v-if=\"phoneLoading\" class=\"loading-icon\">⏳</view>\n\t\t\t\t\t{{ phoneLoading ? '登录中...' : '登录' }}\n\t\t\t\t</button>\n\t\t\t</view>\n\n\t\t\t<!-- 底部操作 -->\n\t\t\t<view class=\"login-footer\">\n\t\t\t\t<view class=\"footer-link\" @click=\"goToRegister\">\n\t\t\t\t\t还没有账号？立即注册\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { loginApi, phoneLogin, getCodeImg, sendPhoneCode } from '@/api/user'\n\timport { utils } from '@/api/index'\n\timport { setToken, setUserInfo, disableGuard } from '@/utils/auth'\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 当前标签页\n\t\t\t\tactiveTab: 'password',\n\n\t\t\t\t// 密码登录表单\n\t\t\t\tpasswordForm: {\n\t\t\t\t\tusername: '',\n\t\t\t\t\tpassword: '',\n\t\t\t\t\tcode: '',\n\t\t\t\t\tuuid: '',\n\t\t\t\t\trememberMe: false\n\t\t\t\t},\n\n\t\t\t\t// 手机登录表单\n\t\t\t\tphoneForm: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\tphoneCode: ''\n\t\t\t\t},\n\n\t\t\t\t// 状态控制\n\t\t\t\tpasswordLoading: false,\n\t\t\t\tphoneLoading: false,\n\t\t\t\tshowPassword: false,\n\t\t\t\tcaptchaEnabled: true,\n\t\t\t\tcodeUrl: '',\n\n\t\t\t\t// 短信验证码\n\t\t\t\tsmsDisabled: false,\n\t\t\t\tsmsButtonText: '获取验证码',\n\t\t\t\tsmsCountdown: 0\n\t\t\t}\n\t\t},\n\n\t\tonLoad() {\n\t\t\tthis.initPage()\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 初始化页面\n\t\t\tasync initPage() {\n\t\t\t\t// 获取验证码\n\t\t\t\tawait this.refreshCaptcha()\n\t\t\t\t// 获取记住的密码\n\t\t\t\tthis.loadRememberedPassword()\n\t\t\t},\n\n\t\t\t// 切换标签页\n\t\t\tswitchTab(tab) {\n\t\t\t\tthis.activeTab = tab\n\t\t\t},\n\n\t\t\t// 切换密码显示\n\t\t\ttogglePassword() {\n\t\t\t\tthis.showPassword = !this.showPassword\n\t\t\t},\n\n\t\t\t// 切换记住密码\n\t\t\ttoggleRemember() {\n\t\t\t\tthis.passwordForm.rememberMe = !this.passwordForm.rememberMe\n\t\t\t},\n\n\t\t\t// 刷新验证码\n\t\t\tasync refreshCaptcha() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await getCodeImg()\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tthis.captchaEnabled = res.captchaEnabled !== false\n\t\t\t\t\t\tif (this.captchaEnabled) {\n\t\t\t\t\t\t\tthis.codeUrl = \"data:image/gif;base64,\" + res.img\n\t\t\t\t\t\t\tthis.passwordForm.uuid = res.uuid\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取验证码失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载记住的密码\n\t\t\tloadRememberedPassword() {\n\t\t\t\ttry {\n\t\t\t\t\tconst username = uni.getStorageSync('remembered_username')\n\t\t\t\t\tconst password = uni.getStorageSync('remembered_password')\n\t\t\t\t\tconst rememberMe = uni.getStorageSync('remembered_me')\n\n\t\t\t\t\tif (rememberMe && username && password) {\n\t\t\t\t\t\tthis.passwordForm.username = username\n\t\t\t\t\t\t// 尝试解密密码，如果失败则使用原始密码\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tthis.passwordForm.password = utils.decrypt(password)\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('密码解密失败:', error)\n\t\t\t\t\t\t\tthis.passwordForm.password = password\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.passwordForm.rememberMe = true\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('加载记住的密码失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 保存记住的密码\n\t\t\tsaveRememberedPassword() {\n\t\t\t\ttry {\n\t\t\t\t\tif (this.passwordForm.rememberMe) {\n\t\t\t\t\t\tuni.setStorageSync('remembered_username', this.passwordForm.username)\n\t\t\t\t\t\t// 尝试加密密码，如果失败则直接存储\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst encryptedPassword = utils.encrypt(this.passwordForm.password)\n\t\t\t\t\t\t\tuni.setStorageSync('remembered_password', encryptedPassword)\n\t\t\t\t\t\t\tconsole.log('密码加密成功')\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('密码加密失败，使用原始密码存储:', error)\n\t\t\t\t\t\t\t// 如果加密失败，直接存储原始密码（不推荐，但保证功能可用）\n\t\t\t\t\t\t\tuni.setStorageSync('remembered_password', this.passwordForm.password)\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.setStorageSync('remembered_me', true)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.removeStorageSync('remembered_username')\n\t\t\t\t\t\tuni.removeStorageSync('remembered_password')\n\t\t\t\t\t\tuni.removeStorageSync('remembered_me')\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('保存记住的密码失败:', error)\n\t\t\t\t\t// 即使保存失败，也不应该影响登录流程\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 密码登录\n\t\t\tasync handlePasswordLogin() {\n\t\t\t\tif (!this.passwordForm.username) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入用户名',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!this.passwordForm.password) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入密码',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (this.captchaEnabled && !this.passwordForm.code) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入验证码',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.passwordLoading = true\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await loginApi(\n\t\t\t\t\t\tthis.passwordForm.username,\n\t\t\t\t\t\tthis.passwordForm.password,\n\t\t\t\t\t\tthis.passwordForm.code,\n\t\t\t\t\t\tthis.passwordForm.uuid\n\t\t\t\t\t)\n\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tconsole.log('登录API调用成功:', res)\n\n\t\t\t\t\t\t// 保存登录信息（优先处理，确保登录状态正确保存）\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst token = res.token || res.data.token\n\t\t\t\t\t\t\tconsole.log('准备保存token:', token)\n\t\t\t\t\t\t\tconsole.log('登录响应数据:', res)\n\n\t\t\t\t\t\t\t// 保存token\n\t\t\t\t\t\t\tsetToken(token)\n\n\t\t\t\t\t\t\t// 构造用户信息对象，确保包含必要字段\n\t\t\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\t\t\tusername: res.data.username || this.passwordForm.username,\n\t\t\t\t\t\t\t\tuserId: res.data.userId,\n\t\t\t\t\t\t\t\tavatar: res.data.avatar || '',\n\t\t\t\t\t\t\t\troles: res.data.roles || ['user'],\n\t\t\t\t\t\t\t\tphone: res.data.phone || ''\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconsole.log('准备保存用户信息:', userInfo)\n\t\t\t\t\t\t\tsetUserInfo(userInfo)\n\t\t\t\t\t\t\tconsole.log('登录信息保存成功')\n\n\t\t\t\t\t\t\t// 验证保存结果\n\t\t\t\t\t\t\tconst savedToken = uni.getStorageSync('user_token')\n\t\t\t\t\t\t\tconst savedUserInfo = uni.getStorageSync('user_info')\n\t\t\t\t\t\t\tconsole.log('验证保存的token:', savedToken)\n\t\t\t\t\t\t\tconsole.log('验证保存的用户信息:', savedUserInfo)\n\n\t\t\t\t\t\t\tif (!savedToken) {\n\t\t\t\t\t\t\t\tthrow new Error('Token保存失败')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (!savedUserInfo) {\n\t\t\t\t\t\t\t\tthrow new Error('用户信息保存失败')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('保存登录信息失败:', error)\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '登录状态保存失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 保存记住的密码（不影响登录流程）\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tthis.saveRememberedPassword()\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('保存记住密码失败，但不影响登录:', error)\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '登录成功',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t\t})\n\n\t\t\t\t\t\t// 确保token保存后再跳转\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t// 再次验证登录状态\n\t\t\t\t\t\t\tconst finalToken = uni.getStorageSync('user_token')\n\t\t\t\t\t\t\tconst finalUserInfo = uni.getStorageSync('user_info')\n\n\t\t\t\t\t\t\tconsole.log('跳转前最终验证:')\n\t\t\t\t\t\t\tconsole.log('- Token:', finalToken)\n\t\t\t\t\t\t\tconsole.log('- UserInfo:', finalUserInfo)\n\n\t\t\t\t\t\t\tif (!finalToken || !finalUserInfo) {\n\t\t\t\t\t\t\t\tconsole.error('登录信息验证失败，无法跳转')\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '登录状态异常，请重试',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tconsole.log('准备跳转到首页')\n\t\t\t\t\t\t\t// 临时禁用路由守卫，避免跳转时被拦截\n\t\t\t\t\t\t\tdisableGuard()\n\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\turl: '/pages/index/index',\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\tconsole.log('跳转首页成功')\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\t\t\t\tconsole.error('跳转首页失败:', error)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}, 1200)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('登录失败:', res)\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '登录失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t\t// 刷新验证码\n\t\t\t\t\t\tif (this.captchaEnabled) {\n\t\t\t\t\t\t\tawait this.refreshCaptcha()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('登录请求失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '登录失败，请稍后再试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\t// 刷新验证码\n\t\t\t\t\tif (this.captchaEnabled) {\n\t\t\t\t\t\tawait this.refreshCaptcha()\n\t\t\t\t\t}\n\t\t\t\t} finally {\n\t\t\t\t\tthis.passwordLoading = false\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 发送短信验证码\n\t\t\tasync sendSmsCode() {\n\t\t\t\tif (!this.phoneForm.phone) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.phoneForm.phone)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await sendPhoneCode(this.phoneForm.phone)\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '发送成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis.startSmsCountdown()\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '发送失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('发送短信失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '发送失败，请稍后再试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 开始短信倒计时\n\t\t\tstartSmsCountdown() {\n\t\t\t\tthis.smsCountdown = 60\n\t\t\t\tthis.smsDisabled = true\n\t\t\t\tthis.smsButtonText = `${this.smsCountdown}s`\n\n\t\t\t\tconst timer = setInterval(() => {\n\t\t\t\t\tthis.smsCountdown--\n\t\t\t\t\tthis.smsButtonText = `${this.smsCountdown}s`\n\n\t\t\t\t\tif (this.smsCountdown <= 0) {\n\t\t\t\t\t\tclearInterval(timer)\n\t\t\t\t\t\tthis.smsDisabled = false\n\t\t\t\t\t\tthis.smsButtonText = '获取验证码'\n\t\t\t\t\t}\n\t\t\t\t}, 1000)\n\t\t\t},\n\n\t\t\t// 手机登录\n\t\t\tasync handlePhoneLogin() {\n\t\t\t\tif (!this.phoneForm.phone) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!/^1[3-9]\\d{9}$/.test(this.phoneForm.phone)) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入正确的手机号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tif (!this.phoneForm.phoneCode) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入短信验证码',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.phoneLoading = true\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await phoneLogin(this.phoneForm.phone, this.phoneForm.phoneCode)\n\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\t// 保存登录信息\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst token = res.token || res.data.token\n\t\t\t\t\t\t\tconsole.log('手机登录 - 准备保存token:', token)\n\t\t\t\t\t\t\tconsole.log('手机登录 - 响应数据:', res)\n\n\t\t\t\t\t\t\t// 保存token\n\t\t\t\t\t\t\tsetToken(token)\n\n\t\t\t\t\t\t\t// 构造用户信息对象\n\t\t\t\t\t\t\tconst userInfo = {\n\t\t\t\t\t\t\t\tusername: res.data.username || this.phoneForm.phone,\n\t\t\t\t\t\t\t\tuserId: res.data.userId,\n\t\t\t\t\t\t\t\tavatar: res.data.avatar || '',\n\t\t\t\t\t\t\t\troles: res.data.roles || ['user'],\n\t\t\t\t\t\t\t\tphone: this.phoneForm.phone\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconsole.log('手机登录 - 准备保存用户信息:', userInfo)\n\t\t\t\t\t\t\tsetUserInfo(userInfo)\n\t\t\t\t\t\t\tconsole.log('手机登录 - 登录信息保存成功')\n\n\t\t\t\t\t\t\t// 验证保存结果\n\t\t\t\t\t\t\tconst savedToken = uni.getStorageSync('user_token')\n\t\t\t\t\t\t\tconst savedUserInfo = uni.getStorageSync('user_info')\n\t\t\t\t\t\t\tconsole.log('手机登录 - 验证保存的token:', savedToken)\n\t\t\t\t\t\t\tconsole.log('手机登录 - 验证保存的用户信息:', savedUserInfo)\n\n\t\t\t\t\t\t\tif (!savedToken) {\n\t\t\t\t\t\t\t\tthrow new Error('Token保存失败')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (!savedUserInfo) {\n\t\t\t\t\t\t\t\tthrow new Error('用户信息保存失败')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('手机登录 - 保存登录信息失败:', error)\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '登录状态保存失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '登录成功',\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t\t})\n\n\t\t\t\t\t\t// 跳转到首页\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t// 再次验证登录状态\n\t\t\t\t\t\t\tconst finalToken = uni.getStorageSync('user_token')\n\t\t\t\t\t\t\tconst finalUserInfo = uni.getStorageSync('user_info')\n\n\t\t\t\t\t\t\tconsole.log('手机登录 - 跳转前最终验证:')\n\t\t\t\t\t\t\tconsole.log('- Token:', finalToken)\n\t\t\t\t\t\t\tconsole.log('- UserInfo:', finalUserInfo)\n\n\t\t\t\t\t\t\tif (!finalToken || !finalUserInfo) {\n\t\t\t\t\t\t\t\tconsole.error('手机登录 - 登录信息验证失败，无法跳转')\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '登录状态异常，请重试',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tconsole.log('手机登录 - 准备跳转到首页')\n\t\t\t\t\t\t\t// 临时禁用路由守卫，避免跳转时被拦截\n\t\t\t\t\t\t\tdisableGuard()\n\n\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\turl: '/pages/index/index',\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\tconsole.log('手机登录 - 跳转首页成功')\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\tfail: (error) => {\n\t\t\t\t\t\t\t\t\tconsole.error('手机登录 - 跳转首页失败:', error)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}, 1200)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '登录失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('手机登录失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '登录失败，请稍后再试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t} finally {\n\t\t\t\t\tthis.phoneLoading = false\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 前往注册页面\n\t\t\tgoToRegister() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/register/register'\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t/* 登录容器 */\n\t.login-container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 40rpx;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\n\t/* 背景装饰 */\n\t.bg-decoration {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tpointer-events: none;\n\t}\n\n\t.circle {\n\t\tposition: absolute;\n\t\tborder-radius: 50%;\n\t\tbackground: rgba(255, 255, 255, 0.1);\n\t\tanimation: float 6s ease-in-out infinite;\n\t}\n\n\t.circle-1 {\n\t\twidth: 200rpx;\n\t\theight: 200rpx;\n\t\ttop: 10%;\n\t\tleft: 10%;\n\t\tanimation-delay: 0s;\n\t}\n\n\t.circle-2 {\n\t\twidth: 150rpx;\n\t\theight: 150rpx;\n\t\ttop: 60%;\n\t\tright: 15%;\n\t\tanimation-delay: 2s;\n\t}\n\n\t.circle-3 {\n\t\twidth: 100rpx;\n\t\theight: 100rpx;\n\t\tbottom: 20%;\n\t\tleft: 20%;\n\t\tanimation-delay: 4s;\n\t}\n\n\t@keyframes float {\n\t\t0%, 100% {\n\t\t\ttransform: translateY(0px) rotate(0deg);\n\t\t}\n\t\t50% {\n\t\t\ttransform: translateY(-20px) rotate(180deg);\n\t\t}\n\t}\n\n\t/* 登录卡片 */\n\t.login-card {\n\t\tbackground: rgba(255, 255, 255, 0.95);\n\t\tbackdrop-filter: blur(10px);\n\t\tborder-radius: 24rpx;\n\t\tpadding: 60rpx 40rpx;\n\t\twidth: 100%;\n\t\tmax-width: 600rpx;\n\t\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);\n\t\tposition: relative;\n\t\tz-index: 1;\n\t}\n\n\t.fade-in {\n\t\tanimation: fadeInUp 0.8s ease-out;\n\t}\n\n\t@keyframes fadeInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(50rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t/* 登录头部 */\n\t.login-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 60rpx;\n\t}\n\n\t.logo {\n\t\tfont-size: 80rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.subtitle {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t}\n\n\t/* 标签页 */\n\t.tab-container {\n\t\tdisplay: flex;\n\t\tbackground: #f5f5f5;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 8rpx;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.tab-item {\n\t\tflex: 1;\n\t\ttext-align: center;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\ttransition: all 0.3s ease;\n\t\tcursor: pointer;\n\t}\n\n\t.tab-item.active {\n\t\tbackground: #ffffff;\n\t\tcolor: #667eea;\n\t\tfont-weight: bold;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t/* 表单容器 */\n\t.form-container {\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t/* 输入组 */\n\t.input-group {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 0 20rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tborder: 2rpx solid transparent;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.input-group:focus-within {\n\t\tborder-color: #667eea;\n\t\tbackground: #ffffff;\n\t\tbox-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);\n\t}\n\n\t.input-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 20rpx;\n\t\tcolor: #999999;\n\t}\n\n\t.input-field {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tbackground: transparent;\n\t\tborder: none;\n\t}\n\n\t.input-suffix {\n\t\tfont-size: 32rpx;\n\t\tcolor: #999999;\n\t\tcursor: pointer;\n\t\tpadding: 10rpx;\n\t}\n\n\t/* 验证码相关 */\n\t.captcha-group {\n\t\tpadding-right: 0;\n\t}\n\n\t.captcha-input {\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.captcha-image {\n\t\twidth: 160rpx;\n\t\theight: 80rpx;\n\t\tborder-radius: 8rpx;\n\t\toverflow: hidden;\n\t\tcursor: pointer;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground: #e9ecef;\n\t}\n\n\t.captcha-img {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t.captcha-placeholder {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t}\n\n\t/* 短信验证码 */\n\t.sms-group {\n\t\tpadding-right: 0;\n\t}\n\n\t.sms-input {\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.sms-btn {\n\t\tpadding: 20rpx 30rpx;\n\t\tbackground: #667eea;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 12rpx;\n\t\tfont-size: 24rpx;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.sms-btn:disabled {\n\t\tbackground: #c0c4cc;\n\t\tcursor: not-allowed;\n\t}\n\n\t/* 复选框 */\n\t.checkbox-group {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.checkbox {\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tborder: 2rpx solid #ddd;\n\t\tborder-radius: 6rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-right: 15rpx;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.checkbox.checked {\n\t\tbackground: #667eea;\n\t\tborder-color: #667eea;\n\t}\n\n\t.checkbox-icon {\n\t\tcolor: #ffffff;\n\t\tfont-size: 20rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.checkbox-label {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666666;\n\t}\n\n\t/* 登录按钮 */\n\t.login-btn {\n\t\twidth: 100%;\n\t\theight: 88rpx;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 44rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t.login-btn:hover {\n\t\ttransform: translateY(-2rpx);\n\t\tbox-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);\n\t}\n\n\t.login-btn.loading,\n\t.login-btn:disabled {\n\t\tbackground: #c0c4cc;\n\t\tcursor: not-allowed;\n\t\ttransform: none;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(192, 196, 204, 0.3);\n\t}\n\n\t.loading-icon {\n\t\tmargin-right: 15rpx;\n\t\tanimation: spin 1s linear infinite;\n\t}\n\n\t@keyframes spin {\n\t\tfrom {\n\t\t\ttransform: rotate(0deg);\n\t\t}\n\t\tto {\n\t\t\ttransform: rotate(360deg);\n\t\t}\n\t}\n\n\t/* 底部 */\n\t.login-footer {\n\t\ttext-align: center;\n\t\tmargin-top: 40rpx;\n\t}\n\n\t.footer-link {\n\t\tcolor: #667eea;\n\t\tfont-size: 26rpx;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.footer-link:hover {\n\t\tcolor: #764ba2;\n\t}\n\n\t/* 响应式设计 */\n\t@media screen and (max-width: 750rpx) {\n\t\t.login-card {\n\t\t\tpadding: 40rpx 30rpx;\n\t\t}\n\n\t\t.title {\n\t\t\tfont-size: 42rpx;\n\t\t}\n\n\t\t.subtitle {\n\t\t\tfont-size: 26rpx;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./login.vue?vue&type=style&index=0&id=b237504c&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755076432174\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}