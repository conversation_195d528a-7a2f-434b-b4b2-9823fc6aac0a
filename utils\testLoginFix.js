// 测试登录跳转修复
import { getToken, setToken, removeToken, isLoggedIn, checkLogin } from '@/utils/auth'

// 模拟登录流程测试
export function testLoginFlow() {
    console.log('=== 测试登录流程修复 ===')
    
    // 1. 清除现有登录状态
    console.log('\n1. 清除现有登录状态')
    removeToken()
    console.log('登录状态:', isLoggedIn())
    
    // 2. 模拟登录成功，保存token
    console.log('\n2. 模拟登录成功')
    const mockToken = 'test_token_' + Date.now()
    const mockUserInfo = {
        id: 1,
        username: 'testuser',
        phone: '13800138000'
    }
    
    console.log('保存token:', mockToken)
    setToken(mockToken)
    
    console.log('保存用户信息:', mockUserInfo)
    uni.setStorageSync('user_info', mockUserInfo)
    
    // 3. 验证登录状态
    console.log('\n3. 验证登录状态')
    const savedToken = getToken()
    console.log('获取到的token:', savedToken)
    console.log('登录状态:', isLoggedIn())
    
    // 4. 测试token一致性
    console.log('\n4. 测试token一致性')
    const authToken = uni.getStorageSync('user_token')
    const requestToken = uni.getStorageSync('user_token') // 现在request.js也使用user_token
    console.log('auth.js中的token:', authToken)
    console.log('request.js中的token:', requestToken)
    console.log('token一致性:', authToken === requestToken)
    
    return {
        tokenSaved: !!savedToken,
        isLoggedIn: isLoggedIn(),
        tokenConsistency: authToken === requestToken
    }
}

// 测试路由守卫
export function testRouteGuard() {
    console.log('\n=== 测试路由守卫 ===')
    
    // 确保已登录
    if (!isLoggedIn()) {
        console.log('用户未登录，先设置登录状态')
        setToken('test_token_for_guard')
        uni.setStorageSync('user_info', { username: 'testuser' })
    }
    
    console.log('当前登录状态:', isLoggedIn())
    
    // 测试受保护页面列表
    const protectedPages = [
        '/pages/index/index',
        '/pages/activation/activation',
        '/pages/signin/signin'
    ]
    
    console.log('受保护的页面:', protectedPages)
    
    return {
        isLoggedIn: isLoggedIn(),
        protectedPages: protectedPages
    }
}

// 测试完整的登录到跳转流程
export function testCompleteLoginFlow() {
    console.log('\n=== 测试完整登录流程 ===')
    
    return new Promise((resolve) => {
        // 1. 清除登录状态
        removeToken()
        console.log('1. 清除登录状态完成')
        
        // 2. 模拟登录API响应
        const mockLoginResponse = {
            code: 200,
            token: 'mock_token_' + Date.now(),
            data: {
                id: 1,
                username: 'testuser',
                phone: '13800138000',
                email: '<EMAIL>'
            }
        }
        
        console.log('2. 模拟登录API响应:', mockLoginResponse)
        
        // 3. 模拟登录成功处理逻辑
        try {
            const token = mockLoginResponse.token || mockLoginResponse.data.token
            console.log('3. 准备保存token:', token)
            
            setToken(token)
            uni.setStorageSync('user_info', mockLoginResponse.data)
            console.log('3. 登录信息保存成功')
            
            // 验证token是否保存成功
            const savedToken = uni.getStorageSync('user_token')
            console.log('3. 验证保存的token:', savedToken)
            
            if (!savedToken) {
                throw new Error('Token保存失败')
            }
            
            // 4. 检查登录状态
            const loginStatus = isLoggedIn()
            console.log('4. 登录状态检查:', loginStatus)
            
            // 5. 模拟跳转前的状态
            console.log('5. 准备跳转到首页')
            console.log('5. 当前登录状态:', isLoggedIn())
            console.log('5. 当前token:', getToken())
            
            resolve({
                success: true,
                token: savedToken,
                isLoggedIn: loginStatus,
                userInfo: mockLoginResponse.data
            })
            
        } catch (error) {
            console.error('登录流程测试失败:', error)
            resolve({
                success: false,
                error: error.message
            })
        }
    })
}

// 运行所有测试
export async function runAllLoginTests() {
    console.log('开始运行登录修复测试...\n')
    
    const loginFlowResult = testLoginFlow()
    const routeGuardResult = testRouteGuard()
    const completeFlowResult = await testCompleteLoginFlow()
    
    console.log('\n=== 测试结果汇总 ===')
    console.log('基础登录流程:', loginFlowResult)
    console.log('路由守卫测试:', routeGuardResult)
    console.log('完整登录流程:', completeFlowResult)
    
    const allSuccess = loginFlowResult.tokenSaved && 
                      loginFlowResult.isLoggedIn && 
                      loginFlowResult.tokenConsistency && 
                      completeFlowResult.success
    
    console.log('\n=== 最终结果 ===')
    console.log('所有测试:', allSuccess ? '✅ 通过' : '❌ 失败')
    
    if (allSuccess) {
        console.log('\n🎉 恭喜！登录跳转问题已经修复！')
        console.log('现在登录成功后应该可以正常跳转到首页了。')
    } else {
        console.log('\n⚠️  仍然存在一些问题，需要进一步调试。')
    }
    
    return {
        loginFlow: loginFlowResult,
        routeGuard: routeGuardResult,
        completeFlow: completeFlowResult,
        overall: allSuccess
    }
}
