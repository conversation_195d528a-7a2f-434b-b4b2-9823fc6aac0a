<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>

		<!-- 登录卡片 -->
		<view class="login-card fade-in">
			<!-- 头部 -->
			<view class="login-header">
				<view class="logo">🔐</view>
				<view class="title">欢迎使用本系统</view>
				<view class="subtitle">请选择登录方式</view>
			</view>

			<!-- 标签页切换 -->
			<view class="tab-container">
				<view class="tab-item" :class="{ active: activeTab === 'password' }" @click="switchTab('password')">
					密码登录
				</view>
				<view class="tab-item" :class="{ active: activeTab === 'phone' }" @click="switchTab('phone')">
					手机登录
				</view>
			</view>

			<!-- 密码登录表单 -->
			<view v-if="activeTab === 'password'" class="form-container">
				<view class="input-group">
					<view class="input-icon">👤</view>
					<input v-model="passwordForm.username" placeholder="请输入用户名/手机号" class="input-field" />
				</view>

				<view class="input-group">
					<view class="input-icon">🔒</view>
					<input v-model="passwordForm.password" placeholder="请输入密码" class="input-field" 
						:password="!showPassword" />
					<view class="input-suffix" @click="togglePassword">
						{{ showPassword ? '🙈' : '👁️' }}
					</view>
				</view>

				<!-- 验证码 -->
				<view v-if="captchaEnabled" class="input-group captcha-group">
					<view class="input-icon">🔢</view>
					<input v-model="passwordForm.code" placeholder="请输入验证码" class="input-field captcha-input" />
					<view class="captcha-image" @click="refreshCaptcha">
						<image v-if="codeUrl" :src="codeUrl" class="captcha-img" mode="aspectFit" />
						<view v-else class="captcha-placeholder">点击获取</view>
					</view>
				</view>

				<!-- 记住密码 -->
				<view class="checkbox-group">
					<view class="checkbox" :class="{ checked: passwordForm.rememberMe }" @click="toggleRemember">
						<view class="checkbox-icon">{{ passwordForm.rememberMe ? '✓' : '' }}</view>
					</view>
					<text class="checkbox-label">记住密码</text>
				</view>

				<!-- 登录按钮 -->
				<button class="login-btn" :class="{ loading: passwordLoading }" :disabled="passwordLoading" 
					@click="handlePasswordLogin">
					<view v-if="passwordLoading" class="loading-icon">⏳</view>
					{{ passwordLoading ? '登录中...' : '登录' }}
				</button>
			</view>

			<!-- 手机登录表单 -->
			<view v-if="activeTab === 'phone'" class="form-container">
				<view class="input-group">
					<view class="input-icon">📱</view>
					<input v-model="phoneForm.phone" placeholder="请输入手机号" class="input-field" type="number" />
				</view>

				<view class="input-group sms-group">
					<view class="input-icon">💬</view>
					<input v-model="phoneForm.phoneCode" placeholder="请输入短信验证码" class="input-field sms-input" />
					<button class="sms-btn" :disabled="smsDisabled" @click="sendSmsCode">
						{{ smsButtonText }}
					</button>
				</view>

				<!-- 登录按钮 -->
				<button class="login-btn" :class="{ loading: phoneLoading }" :disabled="phoneLoading" 
					@click="handlePhoneLogin">
					<view v-if="phoneLoading" class="loading-icon">⏳</view>
					{{ phoneLoading ? '登录中...' : '登录' }}
				</button>
			</view>

			<!-- 底部操作 -->
			<view class="login-footer">
				<view class="footer-link" @click="goToRegister">
					还没有账号？立即注册
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { loginApi, phoneLogin, getCodeImg, sendPhoneCode } from '@/api/user'
	import { utils } from '@/api/index'
	import { setToken, setUserInfo, disableGuard } from '@/utils/auth'

	export default {
		data() {
			return {
				// 当前标签页
				activeTab: 'password',

				// 密码登录表单
				passwordForm: {
					username: '',
					password: '',
					code: '',
					uuid: '',
					rememberMe: false
				},

				// 手机登录表单
				phoneForm: {
					phone: '',
					phoneCode: ''
				},

				// 状态控制
				passwordLoading: false,
				phoneLoading: false,
				showPassword: false,
				captchaEnabled: true,
				codeUrl: '',

				// 短信验证码
				smsDisabled: false,
				smsButtonText: '获取验证码',
				smsCountdown: 0
			}
		},

		onLoad() {
			this.initPage()
		},

		methods: {
			// 初始化页面
			async initPage() {
				// 获取验证码
				await this.refreshCaptcha()
				// 获取记住的密码
				this.loadRememberedPassword()
			},

			// 切换标签页
			switchTab(tab) {
				this.activeTab = tab
			},

			// 切换密码显示
			togglePassword() {
				this.showPassword = !this.showPassword
			},

			// 切换记住密码
			toggleRemember() {
				this.passwordForm.rememberMe = !this.passwordForm.rememberMe
			},

			// 刷新验证码
			async refreshCaptcha() {
				try {
					const res = await getCodeImg()
					if (res.code === 200) {
						this.captchaEnabled = res.captchaEnabled !== false
						if (this.captchaEnabled) {
							this.codeUrl = "data:image/gif;base64," + res.img
							this.passwordForm.uuid = res.uuid
						}
					}
				} catch (error) {
					console.error('获取验证码失败:', error)
				}
			},

			// 加载记住的密码
			loadRememberedPassword() {
				try {
					const username = uni.getStorageSync('remembered_username')
					const password = uni.getStorageSync('remembered_password')
					const rememberMe = uni.getStorageSync('remembered_me')

					if (rememberMe && username && password) {
						this.passwordForm.username = username
						// 尝试解密密码，如果失败则使用原始密码
						try {
							this.passwordForm.password = utils.decrypt(password)
						} catch (error) {
							console.error('密码解密失败:', error)
							this.passwordForm.password = password
						}
						this.passwordForm.rememberMe = true
					}
				} catch (error) {
					console.error('加载记住的密码失败:', error)
				}
			},

			// 保存记住的密码
			saveRememberedPassword() {
				try {
					if (this.passwordForm.rememberMe) {
						uni.setStorageSync('remembered_username', this.passwordForm.username)
						// 尝试加密密码，如果失败则直接存储
						try {
							const encryptedPassword = utils.encrypt(this.passwordForm.password)
							uni.setStorageSync('remembered_password', encryptedPassword)
							console.log('密码加密成功')
						} catch (error) {
							console.error('密码加密失败，使用原始密码存储:', error)
							// 如果加密失败，直接存储原始密码（不推荐，但保证功能可用）
							uni.setStorageSync('remembered_password', this.passwordForm.password)
						}
						uni.setStorageSync('remembered_me', true)
					} else {
						uni.removeStorageSync('remembered_username')
						uni.removeStorageSync('remembered_password')
						uni.removeStorageSync('remembered_me')
					}
				} catch (error) {
					console.error('保存记住的密码失败:', error)
					// 即使保存失败，也不应该影响登录流程
				}
			},

			// 密码登录
			async handlePasswordLogin() {
				if (!this.passwordForm.username) {
					uni.showToast({
						title: '请输入用户名',
						icon: 'none'
					})
					return
				}

				if (!this.passwordForm.password) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					})
					return
				}

				if (this.captchaEnabled && !this.passwordForm.code) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none'
					})
					return
				}

				this.passwordLoading = true

				try {
					const res = await loginApi(
						this.passwordForm.username,
						this.passwordForm.password,
						this.passwordForm.code,
						this.passwordForm.uuid
					)

					console.log('登录API响应:', res)

					// 检查响应是否有效
					if (!res) {
						throw new Error('服务器无响应')
					}

					if (res.code === 200 || res.status === 200 || res.success) {
						console.log('登录API调用成功:', res)

						// 保存登录信息（优先处理，确保登录状态正确保存）
						try {
							const token = res.token || res.data.token
							console.log('准备保存token:', token)
							console.log('登录响应数据:', res)

							// 保存token
							setToken(token)

							// 构造用户信息对象，确保包含必要字段
							const userInfo = {
								username: res.data.username || this.passwordForm.username,
								userId: res.data.userId,
								avatar: res.data.avatar || '',
								roles: res.data.roles || ['user'],
								phone: res.data.phone || ''
							}
							console.log('准备保存用户信息:', userInfo)
							setUserInfo(userInfo)
							console.log('登录信息保存成功')

							// 验证保存结果
							const savedToken = uni.getStorageSync('user_token')
							const savedUserInfo = uni.getStorageSync('user_info')
							console.log('验证保存的token:', savedToken)
							console.log('验证保存的用户信息:', savedUserInfo)

							if (!savedToken) {
								throw new Error('Token保存失败')
							}
							if (!savedUserInfo) {
								throw new Error('用户信息保存失败')
							}
						} catch (error) {
							console.error('保存登录信息失败:', error)
							uni.showToast({
								title: '登录状态保存失败',
								icon: 'none'
							})
							return
						}

						// 保存记住的密码（不影响登录流程）
						try {
							this.saveRememberedPassword()
						} catch (error) {
							console.error('保存记住密码失败，但不影响登录:', error)
						}

						uni.showToast({
							title: '登录成功',
							icon: 'success',
							duration: 1000
						})

						// 确保token保存后再跳转
						setTimeout(() => {
							// 再次验证登录状态
							const finalToken = uni.getStorageSync('user_token')
							const finalUserInfo = uni.getStorageSync('user_info')

							console.log('跳转前最终验证:')
							console.log('- Token:', finalToken)
							console.log('- UserInfo:', finalUserInfo)

							if (!finalToken || !finalUserInfo) {
								console.error('登录信息验证失败，无法跳转')
								uni.showToast({
									title: '登录状态异常，请重试',
									icon: 'none'
								})
								return
							}

							console.log('准备跳转到首页')
							// 临时禁用路由守卫，避免跳转时被拦截
							disableGuard()

							uni.reLaunch({
								url: '/pages/index/index',
								success: () => {
									console.log('跳转首页成功')
								},
								fail: (error) => {
									console.error('跳转首页失败:', error)
								}
							})
						}, 1200)
					} else {
						console.error('登录失败:', res)
						uni.showToast({
							title: res.msg || '登录失败',
							icon: 'none'
						})
						// 刷新验证码
						if (this.captchaEnabled) {
							await this.refreshCaptcha()
						}
					}
				} catch (error) {
					console.error('登录请求失败:', error)

					let errorMessage = '登录失败，请稍后再试'

					// 根据错误类型提供更具体的错误信息
					if (error.message) {
						if (error.message.includes('请先配置正确的 API 地址')) {
							errorMessage = 'API地址未配置，请联系管理员'
						} else if (error.message.includes('网络')) {
							errorMessage = '网络连接失败，请检查网络'
						} else if (error.message.includes('超时')) {
							errorMessage = '请求超时，请重试'
						} else {
							errorMessage = error.message
						}
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})

					// 刷新验证码
					if (this.captchaEnabled) {
						await this.refreshCaptcha()
					}
				} finally {
					this.passwordLoading = false
				}
			},

			// 发送短信验证码
			async sendSmsCode() {
				if (!this.phoneForm.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return
				}

				if (!/^1[3-9]\d{9}$/.test(this.phoneForm.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}

				try {
					const res = await sendPhoneCode(this.phoneForm.phone)
					if (res.code === 200) {
						uni.showToast({
							title: '发送成功',
							icon: 'success'
						})
						this.startSmsCountdown()
					} else {
						uni.showToast({
							title: res.msg || '发送失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('发送短信失败:', error)
					uni.showToast({
						title: '发送失败，请稍后再试',
						icon: 'none'
					})
				}
			},

			// 开始短信倒计时
			startSmsCountdown() {
				this.smsCountdown = 60
				this.smsDisabled = true
				this.smsButtonText = `${this.smsCountdown}s`

				const timer = setInterval(() => {
					this.smsCountdown--
					this.smsButtonText = `${this.smsCountdown}s`

					if (this.smsCountdown <= 0) {
						clearInterval(timer)
						this.smsDisabled = false
						this.smsButtonText = '获取验证码'
					}
				}, 1000)
			},

			// 手机登录
			async handlePhoneLogin() {
				if (!this.phoneForm.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return
				}

				if (!/^1[3-9]\d{9}$/.test(this.phoneForm.phone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					})
					return
				}

				if (!this.phoneForm.phoneCode) {
					uni.showToast({
						title: '请输入短信验证码',
						icon: 'none'
					})
					return
				}

				this.phoneLoading = true

				try {
					const res = await phoneLogin(this.phoneForm.phone, this.phoneForm.phoneCode)

					if (res.code === 200) {
						// 保存登录信息
						try {
							const token = res.token || res.data.token
							console.log('手机登录 - 准备保存token:', token)
							console.log('手机登录 - 响应数据:', res)

							// 保存token
							setToken(token)

							// 构造用户信息对象
							const userInfo = {
								username: res.data.username || this.phoneForm.phone,
								userId: res.data.userId,
								avatar: res.data.avatar || '',
								roles: res.data.roles || ['user'],
								phone: this.phoneForm.phone
							}
							console.log('手机登录 - 准备保存用户信息:', userInfo)
							setUserInfo(userInfo)
							console.log('手机登录 - 登录信息保存成功')

							// 验证保存结果
							const savedToken = uni.getStorageSync('user_token')
							const savedUserInfo = uni.getStorageSync('user_info')
							console.log('手机登录 - 验证保存的token:', savedToken)
							console.log('手机登录 - 验证保存的用户信息:', savedUserInfo)

							if (!savedToken) {
								throw new Error('Token保存失败')
							}
							if (!savedUserInfo) {
								throw new Error('用户信息保存失败')
							}
						} catch (error) {
							console.error('手机登录 - 保存登录信息失败:', error)
							uni.showToast({
								title: '登录状态保存失败',
								icon: 'none'
							})
							return
						}

						uni.showToast({
							title: '登录成功',
							icon: 'success',
							duration: 1000
						})

						// 跳转到首页
						setTimeout(() => {
							// 再次验证登录状态
							const finalToken = uni.getStorageSync('user_token')
							const finalUserInfo = uni.getStorageSync('user_info')

							console.log('手机登录 - 跳转前最终验证:')
							console.log('- Token:', finalToken)
							console.log('- UserInfo:', finalUserInfo)

							if (!finalToken || !finalUserInfo) {
								console.error('手机登录 - 登录信息验证失败，无法跳转')
								uni.showToast({
									title: '登录状态异常，请重试',
									icon: 'none'
								})
								return
							}

							console.log('手机登录 - 准备跳转到首页')
							// 临时禁用路由守卫，避免跳转时被拦截
							disableGuard()

							uni.reLaunch({
								url: '/pages/index/index',
								success: () => {
									console.log('手机登录 - 跳转首页成功')
								},
								fail: (error) => {
									console.error('手机登录 - 跳转首页失败:', error)
								}
							})
						}, 1200)
					} else {
						uni.showToast({
							title: res.msg || '登录失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('手机登录失败:', error)
					uni.showToast({
						title: '登录失败，请稍后再试',
						icon: 'none'
					})
				} finally {
					this.phoneLoading = false
				}
			},

			// 前往注册页面
			goToRegister() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			}
		}
	}
</script>

<style scoped>
	/* 登录容器 */
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
		position: relative;
		overflow: hidden;
	}

	/* 背景装饰 */
	.bg-decoration {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
	}

	.circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(255, 255, 255, 0.1);
		animation: float 6s ease-in-out infinite;
	}

	.circle-1 {
		width: 200rpx;
		height: 200rpx;
		top: 10%;
		left: 10%;
		animation-delay: 0s;
	}

	.circle-2 {
		width: 150rpx;
		height: 150rpx;
		top: 60%;
		right: 15%;
		animation-delay: 2s;
	}

	.circle-3 {
		width: 100rpx;
		height: 100rpx;
		bottom: 20%;
		left: 20%;
		animation-delay: 4s;
	}

	@keyframes float {
		0%, 100% {
			transform: translateY(0px) rotate(0deg);
		}
		50% {
			transform: translateY(-20px) rotate(180deg);
		}
	}

	/* 登录卡片 */
	.login-card {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		border-radius: 24rpx;
		padding: 60rpx 40rpx;
		width: 100%;
		max-width: 600rpx;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
		position: relative;
		z-index: 1;
	}

	.fade-in {
		animation: fadeInUp 0.8s ease-out;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(50rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 登录头部 */
	.login-header {
		text-align: center;
		margin-bottom: 60rpx;
	}

	.logo {
		font-size: 80rpx;
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 48rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}

	.subtitle {
		font-size: 28rpx;
		color: #666666;
	}

	/* 标签页 */
	.tab-container {
		display: flex;
		background: #f5f5f5;
		border-radius: 12rpx;
		padding: 8rpx;
		margin-bottom: 40rpx;
	}

	.tab-item {
		flex: 1;
		text-align: center;
		padding: 20rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #666666;
		transition: all 0.3s ease;
		cursor: pointer;
	}

	.tab-item.active {
		background: #ffffff;
		color: #667eea;
		font-weight: bold;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	/* 表单容器 */
	.form-container {
		margin-bottom: 40rpx;
	}

	/* 输入组 */
	.input-group {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border-radius: 16rpx;
		padding: 0 20rpx;
		margin-bottom: 30rpx;
		border: 2rpx solid transparent;
		transition: all 0.3s ease;
	}

	.input-group:focus-within {
		border-color: #667eea;
		background: #ffffff;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
	}

	.input-icon {
		font-size: 32rpx;
		margin-right: 20rpx;
		color: #999999;
	}

	.input-field {
		flex: 1;
		height: 80rpx;
		font-size: 28rpx;
		color: #333333;
		background: transparent;
		border: none;
	}

	.input-suffix {
		font-size: 32rpx;
		color: #999999;
		cursor: pointer;
		padding: 10rpx;
	}

	/* 验证码相关 */
	.captcha-group {
		padding-right: 0;
	}

	.captcha-input {
		flex: 1;
		margin-right: 20rpx;
	}

	.captcha-image {
		width: 160rpx;
		height: 80rpx;
		border-radius: 8rpx;
		overflow: hidden;
		cursor: pointer;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #e9ecef;
	}

	.captcha-img {
		width: 100%;
		height: 100%;
	}

	.captcha-placeholder {
		font-size: 24rpx;
		color: #999999;
	}

	/* 短信验证码 */
	.sms-group {
		padding-right: 0;
	}

	.sms-input {
		flex: 1;
		margin-right: 20rpx;
	}

	.sms-btn {
		padding: 20rpx 30rpx;
		background: #667eea;
		color: #ffffff;
		border: none;
		border-radius: 12rpx;
		font-size: 24rpx;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.sms-btn:disabled {
		background: #c0c4cc;
		cursor: not-allowed;
	}

	/* 复选框 */
	.checkbox-group {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
	}

	.checkbox {
		width: 36rpx;
		height: 36rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 15rpx;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.checkbox.checked {
		background: #667eea;
		border-color: #667eea;
	}

	.checkbox-icon {
		color: #ffffff;
		font-size: 20rpx;
		font-weight: bold;
	}

	.checkbox-label {
		font-size: 26rpx;
		color: #666666;
	}

	/* 登录按钮 */
	.login-btn {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		border: none;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.3s ease;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	}

	.login-btn:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 12rpx 32rpx rgba(102, 126, 234, 0.4);
	}

	.login-btn.loading,
	.login-btn:disabled {
		background: #c0c4cc;
		cursor: not-allowed;
		transform: none;
		box-shadow: 0 8rpx 24rpx rgba(192, 196, 204, 0.3);
	}

	.loading-icon {
		margin-right: 15rpx;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 底部 */
	.login-footer {
		text-align: center;
		margin-top: 40rpx;
	}

	.footer-link {
		color: #667eea;
		font-size: 26rpx;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.footer-link:hover {
		color: #764ba2;
	}

	/* 响应式设计 */
	@media screen and (max-width: 750rpx) {
		.login-card {
			padding: 40rpx 30rpx;
		}

		.title {
			font-size: 42rpx;
		}

		.subtitle {
			font-size: 26rpx;
		}
	}
</style>
