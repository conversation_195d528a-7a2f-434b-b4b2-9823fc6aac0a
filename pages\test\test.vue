<template>
	<view class="test-container">
		<view class="test-header">
			<text class="test-title">加密解密测试</text>
		</view>
		
		<view class="test-content">
			<view class="test-section">
				<text class="section-title">Base64 编码测试</text>
				<view class="test-item">
					<text>原始文本: {{ base64TestData }}</text>
				</view>
				<view class="test-item">
					<text>编码结果: {{ base64Encoded }}</text>
				</view>
				<view class="test-item">
					<text>解码结果: {{ base64Decoded }}</text>
				</view>
				<view class="test-item">
					<text class="result" :class="base64Success ? 'success' : 'error'">
						Base64 编码测试: {{ base64Success ? '✅ 成功' : '❌ 失败' }}
					</text>
				</view>
			</view>
			
			<view class="test-section">
				<text class="section-title">加密解密测试</text>
				<view class="test-item">
					<text>原始密码: {{ encryptTestData }}</text>
				</view>
				<view class="test-item">
					<text>加密结果: {{ encrypted }}</text>
				</view>
				<view class="test-item">
					<text>解密结果: {{ decrypted }}</text>
				</view>
				<view class="test-item">
					<text class="result" :class="encryptSuccess ? 'success' : 'error'">
						加密解密测试: {{ encryptSuccess ? '✅ 成功' : '❌ 失败' }}
					</text>
				</view>
			</view>
			
			<view class="test-section">
				<text class="section-title">环境检测</text>
				<view class="test-item">
					<text>uni.base64Encode 可用: {{ hasUniBase64 ? '✅ 是' : '❌ 否' }}</text>
				</view>
				<view class="test-item">
					<text>运行环境: {{ platform }}</text>
				</view>
			</view>
			
			<button class="test-btn" @click="runTests">重新测试</button>
		</view>
	</view>
</template>

<script>
	import { utils } from '@/api/index'
	
	export default {
		data() {
			return {
				base64TestData: 'Hello World! 你好世界！',
				base64Encoded: '',
				base64Decoded: '',
				base64Success: false,
				
				encryptTestData: 'test_password_123',
				encrypted: '',
				decrypted: '',
				encryptSuccess: false,
				
				hasUniBase64: false,
				platform: ''
			}
		},
		
		onLoad() {
			this.runTests()
		},
		
		methods: {
			runTests() {
				console.log('开始运行测试...')

				// 检测环境
				this.hasUniBase64 = typeof uni.base64Encode === 'function'

				// 使用新的 API 获取平台信息
				try {
					if (typeof uni.getDeviceInfo === 'function') {
						this.platform = uni.getDeviceInfo().platform
					} else {
						// 降级到旧 API
						this.platform = uni.getSystemInfoSync().platform
					}
				} catch (error) {
					this.platform = 'unknown'
				}
				
				// 测试 Base64 编码
				try {
					this.base64Encoded = utils.base64Encode(this.base64TestData)
					this.base64Decoded = utils.base64Decode(this.base64Encoded)
					this.base64Success = this.base64TestData === this.base64Decoded
					console.log('Base64 编码测试:', this.base64Success ? '成功' : '失败')
					console.log('原始数据:', this.base64TestData)
					console.log('编码结果:', this.base64Encoded)
					console.log('解码结果:', this.base64Decoded)
				} catch (error) {
					console.error('Base64 编码测试失败:', error)
					this.base64Success = false
				}
				
				// 测试加密解密
				try {
					this.encrypted = utils.encrypt(this.encryptTestData)
					this.decrypted = utils.decrypt(this.encrypted)
					this.encryptSuccess = this.encryptTestData === this.decrypted
					console.log('加密解密测试:', this.encryptSuccess ? '成功' : '失败')
					console.log('原始数据:', this.encryptTestData)
					console.log('加密结果:', this.encrypted)
					console.log('解密结果:', this.decrypted)
				} catch (error) {
					console.error('加密解密测试失败:', error)
					this.encryptSuccess = false
				}
				
				// 显示结果
				const overallSuccess = this.base64Success && this.encryptSuccess
				uni.showToast({
					title: overallSuccess ? '所有测试通过' : '部分测试失败',
					icon: overallSuccess ? 'success' : 'none'
				})
			}
		}
	}
</script>

<style scoped>
	.test-container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.test-header {
		text-align: center;
		padding: 40rpx 0;
	}
	
	.test-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.test-content {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
	}
	
	.test-section {
		margin-bottom: 40rpx;
		padding-bottom: 30rpx;
		border-bottom: 1rpx solid #eee;
	}
	
	.test-section:last-child {
		border-bottom: none;
		margin-bottom: 0;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.test-item {
		margin-bottom: 15rpx;
		padding: 10rpx;
		background-color: #f8f9fa;
		border-radius: 8rpx;
	}
	
	.test-item text {
		font-size: 28rpx;
		color: #666;
		word-break: break-all;
	}
	
	.result {
		font-weight: bold;
	}
	
	.result.success {
		color: #67c23a;
	}
	
	.result.error {
		color: #f56c6c;
	}
	
	.test-btn {
		width: 100%;
		height: 80rpx;
		background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
		color: #fff;
		border: none;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: bold;
		margin-top: 30rpx;
	}
</style>
