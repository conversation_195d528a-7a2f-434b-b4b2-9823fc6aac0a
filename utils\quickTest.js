// 快速测试 Base64 和加密解密功能
import { base64Encode, base64Decode } from '@/utils/base64'
import { utils } from '@/api/index'

// 快速测试 Base64
export function quickTestBase64() {
	console.log('=== 快速测试 Base64 ===')
	
	const testData = 'Hello World! 你好世界！'
	
	try {
		// 直接测试 base64.js 中的函数
		const encoded1 = base64Encode(testData)
		const decoded1 = base64Decode(encoded1)
		const directTest = testData === decoded1
		
		console.log('直接调用 base64.js:')
		console.log('原始:', testData)
		console.log('编码:', encoded1)
		console.log('解码:', decoded1)
		console.log('结果:', directTest ? '✅ 成功' : '❌ 失败')
		
		// 通过 utils 测试
		const encoded2 = utils.base64Encode(testData)
		const decoded2 = utils.base64Decode(encoded2)
		const utilsTest = testData === decoded2
		
		console.log('\n通过 utils 调用:')
		console.log('原始:', testData)
		console.log('编码:', encoded2)
		console.log('解码:', decoded2)
		console.log('结果:', utilsTest ? '✅ 成功' : '❌ 失败')
		
		return {
			direct: directTest,
			utils: utilsTest,
			overall: directTest && utilsTest
		}
		
	} catch (error) {
		console.error('Base64 测试失败:', error)
		return {
			direct: false,
			utils: false,
			overall: false,
			error: error.message
		}
	}
}

// 快速测试加密解密
export function quickTestEncrypt() {
	console.log('\n=== 快速测试加密解密 ===')
	
	const testData = 'password123'
	
	try {
		const encrypted = utils.encrypt(testData)
		const decrypted = utils.decrypt(encrypted)
		const success = testData === decrypted
		
		console.log('原始密码:', testData)
		console.log('加密结果:', encrypted)
		console.log('解密结果:', decrypted)
		console.log('测试结果:', success ? '✅ 成功' : '❌ 失败')
		
		return success
		
	} catch (error) {
		console.error('加密解密测试失败:', error)
		return false
	}
}

// 运行所有快速测试
export function runQuickTests() {
	console.log('=== 开始快速测试 ===')
	
	const base64Results = quickTestBase64()
	const encryptResult = quickTestEncrypt()
	
	console.log('\n=== 快速测试总结 ===')
	console.log('Base64 直接调用:', base64Results.direct ? '✅' : '❌')
	console.log('Base64 utils调用:', base64Results.utils ? '✅' : '❌')
	console.log('加密解密:', encryptResult ? '✅' : '❌')
	
	const allPassed = base64Results.overall && encryptResult
	console.log('总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
	
	if (base64Results.error) {
		console.error('错误信息:', base64Results.error)
	}
	
	return {
		base64: base64Results,
		encrypt: encryptResult,
		overall: allPassed
	}
}

// 在开发环境自动运行快速测试
if (process.env.NODE_ENV === 'development') {
	setTimeout(() => {
		try {
			runQuickTests()
		} catch (error) {
			console.error('快速测试运行失败:', error)
		}
	}, 1000)
}
