







































































































































































/* 页面容器 */
.container.data-v-3120bf0f {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
}
/* 页面头部 */
.header.data-v-3120bf0f {
	text-align: center;
	padding: 60rpx 0 40rpx 0;
	color: #ffffff;
}
.header-title.data-v-3120bf0f {
	font-size: 56rpx;
	font-weight: bold;
	margin-bottom: 15rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.header-subtitle.data-v-3120bf0f {
	font-size: 32rpx;
	opacity: 0.9;
}
/* 导航卡片容器 */
.nav-cards.data-v-3120bf0f {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
	margin-bottom: 40rpx;
}
/* 导航卡片 */
.nav-card.data-v-3120bf0f {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}
.nav-card.data-v-3120bf0f:hover {
	-webkit-transform: translateY(-6rpx);
	        transform: translateY(-6rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}
.nav-card.data-v-3120bf0f::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 6rpx;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}
.activation-card.data-v-3120bf0f::before {
	background: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);
}
.signin-card.data-v-3120bf0f::before {
	background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
}
.nav-card-icon.data-v-3120bf0f {
	font-size: 60rpx;
	margin-bottom: 20rpx;
}
.nav-card-title.data-v-3120bf0f {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 15rpx;
}
.nav-card-desc.data-v-3120bf0f {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
	margin-bottom: 20rpx;
}
.nav-card-arrow.data-v-3120bf0f {
	position: absolute;
	right: 30rpx;
	top: 50%;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
	font-size: 40rpx;
	color: #409EFF;
	font-weight: bold;
}
/* 快速状态 */
.quick-status.data-v-3120bf0f {
	background-color: #ffffff;
	border-radius: 16rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	margin-bottom: 30rpx;
	overflow: hidden;
}
.status-header.data-v-3120bf0f {
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);
}
.status-title.data-v-3120bf0f {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}
.status-content.data-v-3120bf0f {
	padding: 30rpx;
}
.status-item.data-v-3120bf0f {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}
.status-item.data-v-3120bf0f:last-child {
	border-bottom: none;
}
.status-label.data-v-3120bf0f {
	font-size: 28rpx;
	color: #666666;
}
.status-value.data-v-3120bf0f {
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
}
.status-value.success.data-v-3120bf0f {
	color: #67C23A;
}
.status-value.warning.data-v-3120bf0f {
	color: #E6A23C;
}
/* 未验证提示 */
.no-user-tip.data-v-3120bf0f {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 50rpx 30rpx;
	text-align: center;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.tip-icon.data-v-3120bf0f {
	font-size: 80rpx;
	margin-bottom: 30rpx;
}
.tip-text.data-v-3120bf0f {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 40rpx;
	line-height: 1.6;
}
/* 按钮样式 */
.btn.data-v-3120bf0f {
	padding: 25rpx 50rpx;
	border-radius: 30rpx;
	border: none;
	font-size: 30rpx;
	font-weight: bold;
	cursor: pointer;
	transition: all 0.3s ease;
	text-align: center;
	display: inline-block;
	box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}
.btn.data-v-3120bf0f:hover {
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
	box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}
.btn-primary.data-v-3120bf0f {
	background: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);
	color: #ffffff;
}
/* 动画效果 */
.fade-in.data-v-3120bf0f {
	-webkit-animation: fadeInUp-data-v-3120bf0f 0.6s ease-out;
	        animation: fadeInUp-data-v-3120bf0f 0.6s ease-out;
}
@-webkit-keyframes fadeInUp-data-v-3120bf0f {
from {
		opacity: 0;
		-webkit-transform: translateY(30rpx);
		        transform: translateY(30rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes fadeInUp-data-v-3120bf0f {
from {
		opacity: 0;
		-webkit-transform: translateY(30rpx);
		        transform: translateY(30rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}

