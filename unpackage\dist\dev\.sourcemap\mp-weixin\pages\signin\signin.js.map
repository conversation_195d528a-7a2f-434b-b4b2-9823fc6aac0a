{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?ef42", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?ea32", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?09d1", "uni-app:///pages/signin/signin.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?8cf3", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/signin/signin.vue?7173"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "hasSigned", "signInConfig", "dailySignInReward", "enableContinuousReward", "continuousDaysRequired", "continuousReward", "userStats", "continuousDays", "signInHistory", "historyPage", "historyPageSize", "hasMoreHistory", "currentDate", "weekDays", "calendarDates", "signInDates", "computed", "currentYear", "currentMonth", "onLoad", "methods", "initPage", "tokenApi", "tokenRes", "tokenName", "res", "tokenTime", "tokenStatus", "tokenCompCode", "detailRes", "tokenDetail", "console", "loadSignInConfig", "signInApi", "loadUserSignInInfo", "signRes", "loadSignInHistory", "calculateContinuous", "pageSize", "rows", "historyList", "calculateContinuousDays", "today", "signInDate", "lastDate", "yesterday", "expectedDate", "generateMonthlyRecord", "map", "filter", "handleSignIn", "uni", "title", "icon", "duration", "setTimeout", "loadMoreHistory", "generateCalendar", "dates", "day", "i", "date", "isToday", "signed", "disabled", "formatTime", "goToActivation", "url", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACqC;;;AAG1F;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AAAwrB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC6G5sB;AAKA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACA;MACAC;MAEA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAF;cAAA;gBAAAG;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAD;kBACAE;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEAN;cAAA;gBAAAO;gBACA;kBACAC;kBACA,iDACA,iBACAA,YACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAKAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAR;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAM;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAIAD;cAAA;gBAAAE;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACA;cAAA;gBAAA;gBAAA,OAIA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAK;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAGAC;gBAAA;gBAAA,OACAL,4BACA,2BACA,oBACAK,SACA;cAAA;gBAJAb;gBAMA;kBACA;oBACA,oKACAc,MACA;oBACA;kBACA;oBACAC,8BAEA;oBACA;;oBAEA;oBACA;;oBAEA;oBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAT;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAU;MACA;QACA;QACA;MACA;;MAEA;MACAD;QAAA;MAAA;MAEA;MACAE;MAEA;MACA;MAEA;QACA;QACAC;QAEA;UACA;UACA;YACApC;YACAqC;UACA;YACA;YACAC;YACA;cACAtC;cACAqC;YACA;cACA;YACA;UACA;QACA;UACA;UACAE;UAEA;YACAvC;YACAqC;UACA;YACA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAG;MACA;QACA;QACA;QACA;MACA;MAEA;MACA;MAEA,+BACAC;QAAA;MAAA,GACAC;QAAA;MAAA;MAEA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAjB;cAAA;gBAAAR;gBACA;kBACA0B;oBACAC;oBACAC;oBACAC;kBACA;kBAEA;;kBAEA;kBACAZ;kBACA;;kBAEA;kBACA;;kBAEA;kBACA;;kBAEA;kBACAa;oBACA;oBACA;kBACA;gBACA;kBACAJ;oBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAvB;gBACAoB;kBACAC;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MAEA;MACA;MAEA;MACA;MAEA;;MAEA;MACA;QACAC;UACAC;QACA;MACA;;MAEA;MAAA,2BACAC;QACA;QACA;QACA;UAAA,OACAC,+BACAA,6BACAA;QAAA,EACA;QAEA;QAEAH;UACAC;UACAG;UACAC;UACAC;QACA;MAAA;MAhBA;QAAA;MAiBA;MAEA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAf;QACAgB;MACA;IACA;IAEA;IACAC;MACAjB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7cA;AAAA;AAAA;AAAA;AAAqgC,CAAgB,+9BAAG,EAAC,C;;;;;;;;;;;ACAzhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/signin/signin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/signin/signin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./signin.vue?vue&type=template&id=23701386&scoped=true&\"\nvar renderjs\nimport script from \"./signin.vue?vue&type=script&lang=js&\"\nexport * from \"./signin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./signin.vue?vue&type=style&index=0&id=23701386&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"23701386\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/signin/signin.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=template&id=23701386&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.calendarDates, function (date, __i1__) {\n    var $orig = _vm.__get_orig(date)\n    var g0 = date.day || Math.random()\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var l1 = _vm.__map(_vm.signInHistory, function (item, __i2__) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.formatTime(item.signInTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g1 = _vm.signInHistory.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 页面头部 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-title\">每日签到</view>\n\t\t\t<view class=\"header-subtitle\">坚持签到，获得更多额度</view>\n\t\t</view>\n\n\t\t<!-- 签到卡片 -->\n\t\t<view class=\"card fade-in\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<view class=\"card-title\">每日签到</view>\n\t\t\t\t<view class=\"tag\" :class=\"hasSigned ? 'tag-success' : 'tag-info'\">\n\t\t\t\t\t{{ hasSigned ? '今日已签到' : '未签到' }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"card-content\">\n\t\t\t\t<!-- 签到信息 -->\n\t\t\t\t<view class=\"signin-info\">\n\t\t\t\t\t<view class=\"signin-stats\">\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<view class=\"stat-number\">{{ userStats.continuousDays || 0 }}</view>\n\t\t\t\t\t\t\t<view class=\"stat-label\">连续签到天数</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"stat-item\">\n\t\t\t\t\t\t\t<view class=\"stat-number\">{{ signInConfig.dailySignInReward || 5 }}</view>\n\t\t\t\t\t\t\t<view class=\"stat-label\">每日奖励额度</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"signin-tip\" v-if=\"signInConfig.enableContinuousReward\">\n\t\t\t\t\t\t连续签到 {{ signInConfig.continuousDaysRequired || 7 }} 天可额外获得\n\t\t\t\t\t\t{{ signInConfig.continuousReward || 15 }} 额度\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 签到日历 -->\n\t\t\t\t<view class=\"calendar-container\">\n\t\t\t\t\t<view class=\"calendar-header\">\n\t\t\t\t\t\t<view class=\"calendar-title\">{{ currentYear }}年{{ currentMonth }}月</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"calendar-weekdays\">\n\t\t\t\t\t\t<view class=\"weekday\" v-for=\"day in weekDays\" :key=\"day\">{{ day }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"calendar-dates\">\n\t\t\t\t\t\t<view v-for=\"date in calendarDates\" :key=\"date.day || Math.random()\" class=\"calendar-date\"\n\t\t\t\t\t\t\t:class=\"{\n\t\t\t\t\t\t\t\t'empty': !date.day,\n\t\t\t\t\t\t\t\t'signed': date.signed,\n\t\t\t\t\t\t\t\t'today': date.isToday,\n\t\t\t\t\t\t\t\t'disabled': date.disabled\n\t\t\t\t\t\t\t}\">\n\t\t\t\t\t\t\t<text v-if=\"date.day\">{{ date.day }}</text>\n\t\t\t\t\t\t\t<view v-if=\"date.signed\" class=\"signed-mark\">✓</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 签到按钮 -->\n\t\t\t\t<button class=\"btn signin-btn\" :class=\"hasSigned || !userInfo ? 'btn-disabled' : 'btn-primary'\"\n\t\t\t\t\t:disabled=\"hasSigned || !userInfo\" @click=\"handleSignIn\">\n\t\t\t\t\t{{ hasSigned ? '今日已签到' : '立即签到' }}\n\t\t\t\t</button>\n\n\t\t\t\t<!-- 签到历史 -->\n\t\t\t\t<view class=\"signin-history\">\n\t\t\t\t\t<view class=\"history-header\">\n\t\t\t\t\t\t<view class=\"history-title\">签到历史</view>\n\t\t\t\t\t\t<view class=\"history-more\" @click=\"loadMoreHistory\" v-if=\"hasMoreHistory\">\n\t\t\t\t\t\t\t查看更多\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"history-list\">\n\t\t\t\t\t\t<view v-for=\"item in signInHistory\" :key=\"item.id\" class=\"history-item\"\n\t\t\t\t\t\t\t:class=\"item.extraReward ? 'extra-reward' : ''\">\n\t\t\t\t\t\t\t<view class=\"history-content\">\n\t\t\t\t\t\t\t\t<view class=\"history-reward\">获得{{ item.rewardAmount }}额度</view>\n\t\t\t\t\t\t\t\t<view class=\"tag tag-success\" v-if=\"item.extraReward\">连续签到奖励</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"history-time\">{{ formatTime(item.signInTime) }}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"signInHistory.length === 0\" class=\"no-data\">\n\t\t\t\t\t\t\t暂无签到记录\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 提示卡片 -->\n\t\t<view v-if=\"!userInfo\" class=\"card fade-in\" style=\"animation-delay: 0.1s;\">\n\t\t\t<view class=\"card-content\">\n\t\t\t\t<view class=\"no-user-tip\">\n\t\t\t\t\t<view class=\"tip-icon\">🔑</view>\n\t\t\t\t\t<view class=\"tip-text\">请先验证激活码后再进行签到</view>\n\t\t\t\t\t<button class=\"btn btn-primary\" @click=\"goToActivation\">前往激活码管理</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 导航按钮 -->\n\t\t<view class=\"nav-buttons\">\n\t\t\t<button class=\"btn btn-primary nav-btn\" @click=\"goToActivation\">激活码管理</button>\n\t\t\t<button class=\"btn btn-secondary nav-btn\" @click=\"goBack\">返回首页</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\tsignInApi,\n\t\ttokenApi,\n\t\tutils\n\t} from '@/api/index'\n\timport { checkLogin } from '@/utils/auth'\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 用户信息\n\t\t\t\tuserInfo: null,\n\n\t\t\t\t// 签到相关\n\t\t\t\thasSigned: false,\n\t\t\t\tsignInConfig: {\n\t\t\t\t\tdailySignInReward: 5,\n\t\t\t\t\tenableContinuousReward: false,\n\t\t\t\t\tcontinuousDaysRequired: 7,\n\t\t\t\t\tcontinuousReward: 15\n\t\t\t\t},\n\t\t\t\tuserStats: {\n\t\t\t\t\tcontinuousDays: 0\n\t\t\t\t},\n\t\t\t\tsignInHistory: [],\n\t\t\t\thistoryPage: 1,\n\t\t\t\thistoryPageSize: 5,\n\t\t\t\thasMoreHistory: false,\n\n\t\t\t\t// 日历相关\n\t\t\t\tcurrentDate: new Date(),\n\t\t\t\tweekDays: ['日', '一', '二', '三', '四', '五', '六'],\n\t\t\t\tcalendarDates: [],\n\t\t\t\tsignInDates: []\n\t\t\t}\n\t\t},\n\n\t\tcomputed: {\n\t\t\tcurrentYear() {\n\t\t\t\treturn this.currentDate.getFullYear()\n\t\t\t},\n\t\t\tcurrentMonth() {\n\t\t\t\treturn this.currentDate.getMonth() + 1\n\t\t\t}\n\t\t},\n\n\t\tonLoad() {\n\t\t\t// 检查登录状态\n\t\t\tif (!checkLogin()) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\tthis.initPage()\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 初始化页面\n\t\t\tasync initPage() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取用户token状态\n\t\t\t\t\tconst tokenRes = await tokenApi.getUserToken()\n\t\t\t\t\tif (tokenRes.code === 200 && tokenRes.data) {\n\t\t\t\t\t\tconst tokenName = tokenRes.data.tokenName\n\t\t\t\t\t\tif (tokenName) {\n\t\t\t\t\t\t\t// 验证激活码获取用户信息\n\t\t\t\t\t\t\tconst res = await tokenApi.getQuota(tokenName)\n\t\t\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\t\t\ttokenName: tokenName,\n\t\t\t\t\t\t\t\t\ttokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),\n\t\t\t\t\t\t\t\t\ttokenStatus: 0,\n\t\t\t\t\t\t\t\t\ttokenCompCode: '尚未绑定'\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// 尝试获取额外的token信息\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tconst detailRes = await tokenApi.getTokenInfo(tokenName)\n\t\t\t\t\t\t\t\t\tif (detailRes.code === 200 && detailRes.rows && detailRes.rows.length > 0) {\n\t\t\t\t\t\t\t\t\t\tconst tokenDetail = detailRes.rows[0]\n\t\t\t\t\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\t\t\t\t\t...this.userInfo,\n\t\t\t\t\t\t\t\t\t\t\t...tokenDetail\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\tconsole.error('获取token详情失败:', error)\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// 加载签到信息\n\t\t\t\t\t\t\t\tawait this.loadUserSignInInfo()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取用户token失败:', error)\n\t\t\t\t}\n\n\t\t\t\t// 获取签到配置\n\t\t\t\tawait this.loadSignInConfig()\n\t\t\t\t// 生成日历数据\n\t\t\t\tthis.generateCalendar()\n\t\t\t},\n\n\t\t\t// 获取签到配置\n\t\t\tasync loadSignInConfig() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await signInApi.getSetting()\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tthis.signInConfig = res.data\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取签到配置失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载用户签到信息\n\t\t\tasync loadUserSignInInfo() {\n\t\t\t\tif (!this.userInfo) return\n\n\t\t\t\ttry {\n\t\t\t\t\t// 检查今日是否已签到\n\t\t\t\t\tconst signRes = await signInApi.checkSignInToday(this.userInfo.tokenName)\n\t\t\t\t\tif (signRes.code === 200) {\n\t\t\t\t\t\tthis.hasSigned = signRes.data\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('检查签到状态失败:', error)\n\t\t\t\t\tthis.hasSigned = false\n\t\t\t\t}\n\n\t\t\t\t// 加载签到历史\n\t\t\t\tawait this.loadSignInHistory(true)\n\t\t\t},\n\n\t\t\t// 加载签到历史\n\t\t\tasync loadSignInHistory(calculateContinuous = false) {\n\t\t\t\tif (!this.userInfo) return\n\n\t\t\t\ttry {\n\t\t\t\t\tconst pageSize = calculateContinuous ? 30 : this.historyPageSize\n\t\t\t\t\tconst res = await signInApi.getHistory(\n\t\t\t\t\t\tthis.userInfo.tokenName,\n\t\t\t\t\t\tthis.historyPage,\n\t\t\t\t\t\tpageSize\n\t\t\t\t\t)\n\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tif (!calculateContinuous) {\n\t\t\t\t\t\t\tthis.signInHistory = this.historyPage === 1 ? res.rows : [...this.signInHistory, ...res\n\t\t\t\t\t\t\t\t.rows\n\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\tthis.hasMoreHistory = this.signInHistory.length < res.total\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst historyList = res.rows || []\n\n\t\t\t\t\t\t\t// 生成当月签到记录\n\t\t\t\t\t\t\tthis.generateMonthlyRecord(historyList)\n\n\t\t\t\t\t\t\t// 计算连续签到天数\n\t\t\t\t\t\t\tthis.calculateContinuousDays(historyList)\n\n\t\t\t\t\t\t\t// 更新历史记录显示\n\t\t\t\t\t\t\tthis.signInHistory = historyList.slice(0, this.historyPageSize)\n\t\t\t\t\t\t\tthis.hasMoreHistory = historyList.length > this.historyPageSize\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取签到历史失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 计算连续签到天数\n\t\t\tcalculateContinuousDays(historyList) {\n\t\t\t\tif (!historyList || historyList.length === 0) {\n\t\t\t\t\tthis.userStats.continuousDays = 0\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 对签到历史按日期排序（最近的在前）\n\t\t\t\thistoryList.sort((a, b) => new Date(b.signInTime) - new Date(a.signInTime))\n\n\t\t\t\tconst today = new Date()\n\t\t\t\ttoday.setHours(0, 0, 0, 0)\n\n\t\t\t\tlet continuousDays = 0\n\t\t\t\tlet lastDate = null\n\n\t\t\t\tfor (let i = 0; i < historyList.length; i++) {\n\t\t\t\t\tconst signInDate = new Date(historyList[i].signInTime)\n\t\t\t\t\tsignInDate.setHours(0, 0, 0, 0)\n\n\t\t\t\t\tif (i === 0) {\n\t\t\t\t\t\tconst isToday = signInDate.getTime() === today.getTime()\n\t\t\t\t\t\tif (isToday) {\n\t\t\t\t\t\t\tcontinuousDays = 1\n\t\t\t\t\t\t\tlastDate = signInDate\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst yesterday = new Date(today)\n\t\t\t\t\t\t\tyesterday.setDate(today.getDate() - 1)\n\t\t\t\t\t\t\tif (signInDate.getTime() === yesterday.getTime()) {\n\t\t\t\t\t\t\t\tcontinuousDays = 1\n\t\t\t\t\t\t\t\tlastDate = signInDate\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconst expectedDate = new Date(lastDate)\n\t\t\t\t\t\texpectedDate.setDate(expectedDate.getDate() - 1)\n\n\t\t\t\t\t\tif (signInDate.getTime() === expectedDate.getTime()) {\n\t\t\t\t\t\t\tcontinuousDays++\n\t\t\t\t\t\t\tlastDate = signInDate\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tbreak\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.userStats.continuousDays = continuousDays\n\t\t\t},\n\n\t\t\t// 生成当月签到记录\n\t\t\tgenerateMonthlyRecord(historyList) {\n\t\t\t\tif (!historyList || historyList.length === 0) {\n\t\t\t\t\tthis.signInDates = []\n\t\t\t\t\tthis.generateCalendar()\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst currentYear = this.currentYear\n\t\t\t\tconst currentMonth = this.currentMonth - 1\n\n\t\t\t\tthis.signInDates = historyList\n\t\t\t\t\t.map(item => new Date(item.signInTime))\n\t\t\t\t\t.filter(date => date.getFullYear() === currentYear && date.getMonth() === currentMonth)\n\n\t\t\t\tthis.generateCalendar()\n\t\t\t},\n\n\t\t\t// 处理签到\n\t\t\tasync handleSignIn() {\n\t\t\t\tif (!this.userInfo || this.hasSigned) return\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await signInApi.doSignIn(this.userInfo.tokenName)\n\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: `签到成功，获得${res.data.rewardAmount}额度`,\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t})\n\n\t\t\t\t\t\tthis.hasSigned = true\n\n\t\t\t\t\t\t// 增加今天的日期到签到日期列表\n\t\t\t\t\t\tconst today = new Date()\n\t\t\t\t\t\tthis.signInDates.push(today)\n\n\t\t\t\t\t\t// 重新生成日历\n\t\t\t\t\t\tthis.generateCalendar()\n\n\t\t\t\t\t\t// 更新连续签到天数\n\t\t\t\t\t\tthis.userStats.continuousDays += 1\n\n\t\t\t\t\t\t// 更新签到历史\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.historyPage = 1\n\t\t\t\t\t\t\tthis.loadSignInHistory()\n\t\t\t\t\t\t}, 500)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.msg || '签到失败',\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('签到失败:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '签到失败，请稍后再试',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载更多历史\n\t\t\tloadMoreHistory() {\n\t\t\t\tthis.historyPage++\n\t\t\t\tthis.loadSignInHistory()\n\t\t\t},\n\n\t\t\t// 生成日历数据\n\t\t\tgenerateCalendar() {\n\t\t\t\tconst year = this.currentYear\n\t\t\t\tconst month = this.currentMonth - 1\n\n\t\t\t\tconst firstDay = new Date(year, month, 1).getDay()\n\t\t\t\tconst daysInMonth = new Date(year, month + 1, 0).getDate()\n\n\t\t\t\tconst today = new Date()\n\t\t\t\tconst isCurrentMonth = today.getFullYear() === year && today.getMonth() === month\n\n\t\t\t\tlet dates = []\n\n\t\t\t\t// 填充前面的空白\n\t\t\t\tfor (let i = 0; i < firstDay; i++) {\n\t\t\t\t\tdates.push({\n\t\t\t\t\t\tday: null\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\t// 填充日期\n\t\t\t\tfor (let i = 1; i <= daysInMonth; i++) {\n\t\t\t\t\tconst dateObj = new Date(year, month, i)\n\t\t\t\t\tconst isToday = isCurrentMonth && today.getDate() === i\n\t\t\t\t\tconst signed = this.signInDates.some(date =>\n\t\t\t\t\t\tdate.getFullYear() === year &&\n\t\t\t\t\t\tdate.getMonth() === month &&\n\t\t\t\t\t\tdate.getDate() === i\n\t\t\t\t\t)\n\n\t\t\t\t\tconst disabled = dateObj > today\n\n\t\t\t\t\tdates.push({\n\t\t\t\t\t\tday: i,\n\t\t\t\t\t\tisToday,\n\t\t\t\t\t\tsigned,\n\t\t\t\t\t\tdisabled\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\tthis.calendarDates = dates\n\t\t\t},\n\n\t\t\t// 格式化时间\n\t\t\tformatTime(time) {\n\t\t\t\treturn utils.formatDate(time, 'MM-DD HH:mm')\n\t\t\t},\n\n\t\t\t// 前往激活码页面\n\t\t\tgoToActivation() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/activation/activation'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 返回首页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t/* 页面容器 */\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tpadding: 20rpx;\n\t}\n\n\t/* 页面头部 */\n\t.header {\n\t\ttext-align: center;\n\t\tpadding: 40rpx 0;\n\t\tcolor: #ffffff;\n\t}\n\n\t.header-title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 10rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n\t}\n\n\t.header-subtitle {\n\t\tfont-size: 28rpx;\n\t\topacity: 0.9;\n\t}\n\n\t/* 卡片样式 */\n\t.card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\t\tmargin-bottom: 30rpx;\n\t\toverflow: hidden;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.card:hover {\n\t\ttransform: translateY(-4rpx);\n\t\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\n\t}\n\n\t.fade-in {\n\t\tanimation: fadeInUp 0.6s ease-out;\n\t}\n\n\t@keyframes fadeInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(30rpx);\n\t\t}\n\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.card-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\tbackground: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);\n\t}\n\n\t.card-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.card-content {\n\t\tpadding: 30rpx;\n\t}\n\n\t/* 标签样式 */\n\t.tag {\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.tag-success {\n\t\tbackground-color: #f0f9eb;\n\t\tcolor: #67C23A;\n\t\tborder: 1rpx solid #c2e7b0;\n\t}\n\n\t.tag-info {\n\t\tbackground-color: #f4f4f5;\n\t\tcolor: #909399;\n\t\tborder: 1rpx solid #d3d4d6;\n\t}\n\n\t/* 按钮样式 */\n\t.btn {\n\t\tpadding: 20rpx 40rpx;\n\t\tborder-radius: 25rpx;\n\t\tborder: none;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\ttext-align: center;\n\t\tdisplay: inline-block;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.btn:hover {\n\t\ttransform: translateY(-2rpx);\n\t\tbox-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);\n\t}\n\n\t.btn-primary {\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.btn-secondary {\n\t\tbackground: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.btn-disabled {\n\t\tbackground-color: #c0c4cc;\n\t\tcolor: #ffffff;\n\t\tcursor: not-allowed;\n\t\topacity: 0.6;\n\t}\n\n\t.btn-disabled:hover {\n\t\ttransform: none;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t/* 签到信息 */\n\t.signin-info {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.signin-stats {\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.stat-item {\n\t\ttext-align: center;\n\t\tflex: 1;\n\t}\n\n\t.stat-number {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #409EFF;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.stat-label {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n\n\t.signin-tip {\n\t\ttext-align: center;\n\t\tfont-size: 26rpx;\n\t\tcolor: #E6A23C;\n\t\tbackground-color: #fdf6ec;\n\t\tpadding: 15rpx;\n\t\tborder-radius: 8rpx;\n\t\tborder-left: 4rpx solid #E6A23C;\n\t}\n\n\t/* 日历样式 */\n\t.calendar-container {\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 20rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.calendar-header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.calendar-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.calendar-weekdays {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(7, 1fr);\n\t\tgap: 10rpx;\n\t\tmargin-bottom: 15rpx;\n\t\tpadding-bottom: 15rpx;\n\t\tborder-bottom: 1rpx solid #e0e0e0;\n\t}\n\n\t.weekday {\n\t\ttext-align: center;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #666666;\n\t\tpadding: 10rpx 0;\n\t}\n\n\t.calendar-dates {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(7, 1fr);\n\t\tgap: 10rpx;\n\t}\n\n\t.calendar-date {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tborder-radius: 50%;\n\t\tposition: relative;\n\t\tmargin: 0 auto;\n\t\tfont-size: 26rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.calendar-date.empty {\n\t\tvisibility: hidden;\n\t}\n\n\t.calendar-date.signed {\n\t\tbackground: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);\n\t\tcolor: #ffffff;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(103, 194, 58, 0.3);\n\t}\n\n\t.calendar-date.today {\n\t\tborder: 3rpx solid #409EFF;\n\t\tfont-weight: bold;\n\t\tcolor: #409EFF;\n\t}\n\n\t.calendar-date.disabled {\n\t\tcolor: #c0c4cc;\n\t}\n\n\t.signed-mark {\n\t\tposition: absolute;\n\t\tfont-size: 20rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 签到按钮 */\n\t.signin-btn {\n\t\twidth: 100%;\n\t\theight: 80rpx;\n\t\tborder-radius: 40rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(64, 158, 255, 0.3);\n\t}\n\n\t/* 签到历史 */\n\t.signin-history {\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.history-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.history-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.history-more {\n\t\tfont-size: 24rpx;\n\t\tcolor: #409EFF;\n\t\tpadding: 10rpx;\n\t}\n\n\t.history-list {\n\t\tmax-height: 400rpx;\n\t\toverflow-y: auto;\n\t}\n\n\t.history-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 20rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 8rpx;\n\t\tmargin-bottom: 15rpx;\n\t\tborder-left: 4rpx solid #409EFF;\n\t}\n\n\t.history-item.extra-reward {\n\t\tborder-left-color: #67C23A;\n\t\tbackground: linear-gradient(90deg, #f0f9eb 0%, #f8f9fa 100%);\n\t}\n\n\t.history-content {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 15rpx;\n\t}\n\n\t.history-reward {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.history-time {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t}\n\n\t.no-data {\n\t\ttext-align: center;\n\t\tcolor: #999999;\n\t\tpadding: 60rpx 0;\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 无用户提示 */\n\t.no-user-tip {\n\t\ttext-align: center;\n\t\tpadding: 40rpx 20rpx;\n\t}\n\n\t.tip-icon {\n\t\tfont-size: 80rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.tip-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 30rpx;\n\t\tline-height: 1.5;\n\t}\n\n\t/* 导航按钮 */\n\t.nav-buttons {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t\tmargin-top: 30rpx;\n\t}\n\n\t.nav-btn {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tborder-radius: 40rpx;\n\t\tfont-size: 30rpx;\n\t}\n\n\t/* 响应式设计 */\n\t@media screen and (max-width: 750rpx) {\n\t\t.signin-stats {\n\t\t\tflex-direction: column;\n\t\t\tgap: 20rpx;\n\t\t}\n\t}\n\n\t@media screen and (max-width: 600rpx) {\n\t\t.calendar-dates {\n\t\t\tgap: 5rpx;\n\t\t}\n\n\t\t.calendar-date {\n\t\t\twidth: 50rpx;\n\t\t\theight: 50rpx;\n\t\t\tfont-size: 24rpx;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=style&index=0&id=23701386&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./signin.vue?vue&type=style&index=0&id=23701386&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755064676556\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}