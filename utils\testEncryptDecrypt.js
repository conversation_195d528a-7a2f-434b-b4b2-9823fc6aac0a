// 测试加密解密功能
const { base64Encode, base64Decode } = require('./base64-test.js');

// 模拟utils对象的加密解密功能
const utils = {
    // Base64 编码函数
    base64Encode(str) {
        try {
            return base64Encode(String(str));
        } catch (error) {
            console.error('Base64编码失败:', error);
            return String(str);
        }
    },

    // Base64 解码函数
    base64Decode(str) {
        try {
            return base64Decode(String(str));
        } catch (error) {
            console.error('Base64解码失败:', error);
            return String(str);
        }
    },

    // 简单编码函数 (十六进制编码，作为备用方案)
    simpleEncode(str) {
        try {
            str = String(str);
            let result = '';
            for (let i = 0; i < str.length; i++) {
                const hex = str.charCodeAt(i).toString(16);
                result += hex.padStart(2, '0');
            }
            return result;
        } catch (error) {
            console.error('简单编码失败:', error);
            return String(str);
        }
    },

    // 简单解码函数 (十六进制解码)
    simpleDecode(hexStr) {
        try {
            hexStr = String(hexStr);
            let result = '';
            for (let i = 0; i < hexStr.length; i += 2) {
                const hex = hexStr.substr(i, 2);
                const charCode = parseInt(hex, 16);
                if (!isNaN(charCode)) {
                    result += String.fromCharCode(charCode);
                }
            }
            return result;
        } catch (error) {
            console.error('简单解码失败:', error);
            return String(hexStr);
        }
    },

    // 加密函数 (使用 Base64 编码)
    encrypt(data, key = 'default_key', iv = 'default_iv') {
        try {
            // 处理对象类型
            if (typeof data === "object") {
                try {
                    data = JSON.stringify(data);
                } catch (error) {
                    console.log("JSON序列化失败:", error);
                    return data;
                }
            }

            // 转换为字符串
            const dataStr = String(data);

            // 使用异或加密
            const keyStr = key + iv;
            let encrypted = '';

            for (let i = 0; i < dataStr.length; i++) {
                const dataChar = dataStr.charCodeAt(i);
                const keyChar = keyStr.charCodeAt(i % keyStr.length);
                // 异或运算加密
                const encryptedChar = dataChar ^ keyChar;
                encrypted += String.fromCharCode(encryptedChar);
            }

            // 使用 Base64 编码
            return this.base64Encode(encrypted);
        } catch (error) {
            console.error('加密失败:', error);
            // 降级到简单编码
            try {
                return this.simpleEncode(String(data));
            } catch (fallbackError) {
                console.error('降级编码也失败:', fallbackError);
                return String(data);
            }
        }
    },

    // 解密函数 (使用 Base64 解码)
    decrypt(encryptedData, key = 'default_key', iv = 'default_iv') {
        try {
            if (!encryptedData) return '';

            // 使用 Base64 解码
            let decoded;
            try {
                decoded = this.base64Decode(encryptedData);
            } catch (base64Error) {
                console.error('Base64解码失败，尝试简单解码:', base64Error);
                // 降级到简单解码
                decoded = this.simpleDecode(encryptedData);
            }

            if (!decoded) return encryptedData;

            // 使用相同的密钥解密
            const keyStr = key + iv;
            let decrypted = '';

            for (let i = 0; i < decoded.length; i++) {
                const encryptedChar = decoded.charCodeAt(i);
                const keyChar = keyStr.charCodeAt(i % keyStr.length);
                // 异或运算解密
                const decryptedChar = encryptedChar ^ keyChar;
                decrypted += String.fromCharCode(decryptedChar);
            }

            return decrypted;
        } catch (error) {
            console.error('解密失败:', error);
            return String(encryptedData);
        }
    }
};

// 测试用例
const testCases = [
    'Hello World',
    'test123',
    '中文测试',
    'password123',
    '!@#$%^&*()',
    'Hello World! 你好世界！',
    ''
];

console.log('=== 加密解密功能测试 ===');

let allPassed = true;

testCases.forEach((testCase, index) => {
    try {
        console.log(`\n测试 ${index + 1}: "${testCase}"`);
        
        const encrypted = utils.encrypt(testCase);
        console.log('加密结果:', encrypted);
        
        const decrypted = utils.decrypt(encrypted);
        console.log('解密结果:', decrypted);
        
        const passed = testCase === decrypted;
        console.log('测试结果:', passed ? '✅ 通过' : '❌ 失败');
        
        if (!passed) {
            console.error('期望:', testCase);
            console.error('实际:', decrypted);
            console.error('长度比较 - 期望:', testCase.length, '实际:', decrypted.length);
            allPassed = false;
        }
        
    } catch (error) {
        console.error(`测试 ${index + 1} 出错:`, error.message);
        allPassed = false;
    }
});

console.log('\n=== 测试总结 ===');
console.log('总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败');

// 测试不同的密钥
console.log('\n=== 不同密钥测试 ===');
const testPassword = 'mypassword123';
const keys = ['key1', 'key2', 'default_key'];

keys.forEach((key, index) => {
    try {
        console.log(`\n密钥测试 ${index + 1}: "${key}"`);
        
        const encrypted = utils.encrypt(testPassword, key);
        console.log('加密结果:', encrypted);
        
        const decrypted = utils.decrypt(encrypted, key);
        console.log('解密结果:', decrypted);
        
        const passed = testPassword === decrypted;
        console.log('测试结果:', passed ? '✅ 通过' : '❌ 失败');
        
        if (!passed) {
            allPassed = false;
        }
        
    } catch (error) {
        console.error(`密钥测试 ${index + 1} 出错:`, error.message);
        allPassed = false;
    }
});

console.log('\n=== 最终结果 ===');
console.log('所有测试:', allPassed ? '✅ 全部通过' : '❌ 存在问题');
