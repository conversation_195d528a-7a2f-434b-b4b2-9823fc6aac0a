// 网络请求封装
import config from '@/config/index'

const BASE_URL = config.baseURL

// 请求拦截器
const request = (options = {}) => {
	return new Promise((resolve, reject) => {
		// 显示加载提示
		if (options.showLoading !== false) {
			uni.showLoading({
				title: '加载中...',
				mask: true
			})
		}

		// 获取token
		const token = uni.getStorageSync('token') || ''

		// 检查 API 地址是否已配置
		if (BASE_URL.includes('your-api-domain.com')) {
			// 隐藏加载提示
			if (options.showLoading !== false) {
				uni.hideLoading()
			}

			const errorMsg = '请先配置正确的 API 地址'
			if (options.showError !== false) {
				uni.showModal({
					title: '配置提示',
					content: '请在 config/index.js 文件中配置正确的 API 基础地址后再使用',
					showCancel: false
				})
			}
			reject(new Error(errorMsg))
			return
		}

		// 设置默认配置
		const requestConfig = {
			url: BASE_URL + options.url,
			method: options.method || 'GET',
			data: options.data || {},
			header: {
				'Content-Type': 'application/json',
				'Authorization': token ? `Bearer ${token}` : '',
				...options.header
			},
			timeout: options.timeout || 10000
		}

		// 处理GET请求参数
		if (options.params && requestConfig.method === 'GET') {
			// 手动构建查询字符串，兼容微信小程序环境
			const params = Object.keys(options.params)
				.filter(key => options.params[key] !== undefined && options.params[key] !== null)
				.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options.params[key])}`)
				.join('&')

			if (params) {
				requestConfig.url += (requestConfig.url.includes('?') ? '&' : '?') + params
			}
		}

		// 发起请求
		uni.request({
			...requestConfig,
			success: (res) => {
				// 隐藏加载提示
				if (options.showLoading !== false) {
					uni.hideLoading()
				}

				// 处理响应数据
				if (res.statusCode === 200) {
					// 根据业务逻辑处理响应
					if (res.data.code === 200) {
						resolve(res.data)
					} else {
						// 业务错误
						const errorMsg = res.data.msg || res.data.message || '请求失败'
						if (options.showError !== false) {
							uni.showToast({
								title: errorMsg,
								icon: 'none',
								duration: 2000
							})
						}
						reject(new Error(errorMsg))
					}
				} else {
					// HTTP错误
					const errorMsg = `请求失败 (${res.statusCode})`
					if (options.showError !== false) {
						uni.showToast({
							title: errorMsg,
							icon: 'none',
							duration: 2000
						})
					}
					reject(new Error(errorMsg))
				}
			},
			fail: (err) => {
				// 隐藏加载提示
				if (options.showLoading !== false) {
					uni.hideLoading()
				}

				// 网络错误
				const errorMsg = err.errMsg || '网络连接失败'
				if (options.showError !== false) {
					uni.showToast({
						title: errorMsg,
						icon: 'none',
						duration: 2000
					})
				}
				reject(err)
			}
		})
	})
}

// 封装常用请求方法
const http = {
	get: (url, params = {}, options = {}) => {
		return request({
			url,
			method: 'GET',
			params,
			...options
		})
	},

	post: (url, data = {}, options = {}) => {
		return request({
			url,
			method: 'POST',
			data,
			...options
		})
	},

	put: (url, data = {}, options = {}) => {
		return request({
			url,
			method: 'PUT',
			data,
			...options
		})
	},

	delete: (url, params = {}, options = {}) => {
		return request({
			url,
			method: 'DELETE',
			params,
			...options
		})
	}
}

export default http
