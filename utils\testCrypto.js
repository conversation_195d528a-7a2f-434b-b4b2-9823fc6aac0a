// 简单的加密解密测试
import { utils } from '@/api/index'

// 测试 Base64 编码解码
export function testBase64Encode() {
	console.log('=== 测试 Base64 编码解码 ===')

	const testCases = [
		'Hello World',
		'test123',
		'中文测试',
		'!@#$%^&*()',
		'',
		'a',
		'1234567890',
		'The quick brown fox jumps over the lazy dog'
	]

	let allPassed = true

	testCases.forEach((testCase, index) => {
		try {
			const encoded = utils.base64Encode(testCase)
			const decoded = utils.base64Decode(encoded)
			const passed = testCase === decoded

			console.log(`Base64 测试 ${index + 1}:`, {
				original: testCase,
				encoded: encoded,
				decoded: decoded,
				passed: passed
			})

			if (!passed) {
				allPassed = false
			}
		} catch (error) {
			console.error(`Base64 测试 ${index + 1} 失败:`, error)
			allPassed = false
		}
	})

	console.log('Base64 编码解码测试结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
	return allPassed
}

// 测试加密解密
export function testEncryptDecrypt() {
	console.log('=== 测试加密解密 ===')

	const testCases = [
		{ data: 'password123', key: 'mykey', iv: 'myiv' },
		{ data: 'test', key: 'default_key', iv: 'default_iv' },
		{ data: '中文密码', key: 'key中文', iv: 'iv中文' },
		{ data: '', key: 'key', iv: 'iv' },
		{ data: 'a', key: 'b', iv: 'c' }
	]

	let allPassed = true

	testCases.forEach((testCase, index) => {
		try {
			const encrypted = utils.encrypt(testCase.data, testCase.key, testCase.iv)
			const decrypted = utils.decrypt(encrypted, testCase.key, testCase.iv)
			const passed = testCase.data === decrypted

			console.log(`加密测试 ${index + 1}:`, {
				original: testCase.data,
				key: testCase.key,
				iv: testCase.iv,
				encrypted: encrypted,
				decrypted: decrypted,
				passed: passed
			})

			if (!passed) {
				allPassed = false
			}
		} catch (error) {
			console.error(`加密测试 ${index + 1} 失败:`, error)
			allPassed = false
		}
	})

	console.log('加密解密测试结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')
	return allPassed
}

// 运行所有测试
export function runAllTests() {
	console.log('=== 开始运行所有加密解密测试 ===')

	const base64Result = testBase64Encode()
	const encryptDecryptResult = testEncryptDecrypt()

	const allPassed = base64Result && encryptDecryptResult

	console.log('=== 测试总结 ===')
	console.log('Base64 编码解码:', base64Result ? '✅ 通过' : '❌ 失败')
	console.log('加密解密:', encryptDecryptResult ? '✅ 通过' : '❌ 失败')
	console.log('总体结果:', allPassed ? '✅ 全部通过' : '❌ 部分失败')

	return {
		base64: base64Result,
		encryptDecrypt: encryptDecryptResult,
		overall: allPassed
	}
}

// 在开发环境自动运行测试（已禁用，需要时手动调用 runAllTests()）
// if (process.env.NODE_ENV === 'development') {
// 	setTimeout(() => {
// 		try {
// 			runAllTests()
// 		} catch (error) {
// 			console.error('测试运行失败:', error)
// 		}
// 	}, 2000)
// }
