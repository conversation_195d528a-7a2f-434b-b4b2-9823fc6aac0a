<view class="container data-v-57280228"><view class="header data-v-57280228"><view class="header-content data-v-57280228"><view class="header-info data-v-57280228"><view class="header-title data-v-57280228">小说下载器</view><view class="header-subtitle data-v-57280228">激活码管理与每日签到</view></view><view data-event-opts="{{[['tap',[['showUserMenu',['$event']]]]]}}" class="header-user data-v-57280228" bindtap="__e"><view class="user-avatar data-v-57280228">{{$root.g0}}</view><view class="user-name data-v-57280228">{{userDisplayName}}</view><view class="user-arrow data-v-57280228">▼</view></view></view></view><view class="nav-cards data-v-57280228"><view data-event-opts="{{[['tap',[['goToActivation',['$event']]]]]}}" class="nav-card activation-card fade-in data-v-57280228" bindtap="__e"><view class="nav-card-icon data-v-57280228">🔑</view><view class="nav-card-title data-v-57280228">激活码管理</view><view class="nav-card-desc data-v-57280228">获取激活码、验证激活码、查询额度</view><view class="nav-card-arrow data-v-57280228">→</view></view><view data-event-opts="{{[['tap',[['goToSignIn',['$event']]]]]}}" class="nav-card signin-card fade-in data-v-57280228" style="animation-delay:0.1s;" bindtap="__e"><view class="nav-card-icon data-v-57280228">📅</view><view class="nav-card-title data-v-57280228">每日签到</view><view class="nav-card-desc data-v-57280228">每日签到获取额度、查看签到历史</view><view class="nav-card-arrow data-v-57280228">→</view></view></view><block wx:if="{{userInfo}}"><view class="quick-status fade-in data-v-57280228" style="animation-delay:0.2s;"><view class="status-header data-v-57280228"><view class="status-title data-v-57280228">快速状态</view></view><view class="status-content data-v-57280228"><view class="status-item data-v-57280228"><view class="status-label data-v-57280228">激活码状态:</view><view class="status-value success data-v-57280228">已验证</view></view><view class="status-item data-v-57280228"><view class="status-label data-v-57280228">今日签到:</view><view class="{{['status-value','data-v-57280228',hasSigned?'success':'warning']}}">{{''+(hasSigned?'已签到':'未签到')+''}}</view></view><view class="status-item data-v-57280228"><view class="status-label data-v-57280228">剩余额度:</view><view class="status-value data-v-57280228">{{remainingTotalQuota||0}}</view></view></view></view></block><block wx:else><view class="no-user-tip fade-in data-v-57280228" style="animation-delay:0.2s;"><view class="tip-icon data-v-57280228">⚠️</view><view class="tip-text data-v-57280228">您还未验证激活码，请先前往激活码管理页面验证激活码</view><view class="tip-buttons data-v-57280228"><button data-event-opts="{{[['tap',[['goToActivation',['$event']]]]]}}" class="btn btn-primary data-v-57280228" bindtap="__e">立即验证</button><button data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="btn btn-secondary data-v-57280228" bindtap="__e">用户登录</button></view></view></block><block wx:if="{{showUserMenuModal}}"><view data-event-opts="{{[['tap',[['closeUserMenu',['$event']]]]]}}" class="modal-overlay data-v-57280228" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="user-menu-content data-v-57280228" catchtap="__e"><view class="user-menu-header data-v-57280228"><view class="menu-avatar data-v-57280228">{{$root.g1}}</view><view class="menu-user-info data-v-57280228"><view class="menu-username data-v-57280228">{{userDisplayName}}</view><view class="menu-user-id data-v-57280228">{{"ID: "+(currentUser.userId||'N/A')}}</view></view></view><view class="user-menu-list data-v-57280228"><view data-event-opts="{{[['tap',[['goToUserProfile',['$event']]]]]}}" class="menu-item data-v-57280228" bindtap="__e"><view class="menu-icon data-v-57280228">👤</view><view class="menu-text data-v-57280228">个人信息</view><view class="menu-arrow data-v-57280228">→</view></view><view data-event-opts="{{[['tap',[['goToSettings',['$event']]]]]}}" class="menu-item data-v-57280228" bindtap="__e"><view class="menu-icon data-v-57280228">⚙️</view><view class="menu-text data-v-57280228">设置</view><view class="menu-arrow data-v-57280228">→</view></view><view data-event-opts="{{[['tap',[['goToAbout',['$event']]]]]}}" class="menu-item data-v-57280228" bindtap="__e"><view class="menu-icon data-v-57280228">ℹ️</view><view class="menu-text data-v-57280228">关于</view><view class="menu-arrow data-v-57280228">→</view></view><view data-event-opts="{{[['tap',[['handleLogout',['$event']]]]]}}" class="menu-item logout-item data-v-57280228" bindtap="__e"><view class="menu-icon data-v-57280228">🚪</view><view class="menu-text data-v-57280228">退出登录</view><view class="menu-arrow data-v-57280228">→</view></view></view></view></view></block></view>