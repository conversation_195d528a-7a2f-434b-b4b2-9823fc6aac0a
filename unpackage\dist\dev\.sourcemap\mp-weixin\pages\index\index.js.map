{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?d3a5", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?2333", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?4fc9", "uni-app:///pages/index/index.vue", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?17a7", "webpack:///D:/projects/HBuilder/fq_sign_uiapp/pages/index/index.vue?fe61"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "currentUser", "userQuota", "baseLimit", "activityLimit", "tempLimit", "totalLimit", "currentUseNum", "remainingLimit", "<PERSON><PERSON><PERSON><PERSON>", "hasSigned", "showUserMenuModal", "computed", "remainingTotalQuota", "userDisplayName", "onLoad", "console", "setTimeout", "onShow", "methods", "checkLoginAndInit", "initPage", "tokenApi", "tokenRes", "tokenName", "res", "tokenTime", "tokenStatus", "tokenCompCode", "signInApi", "signRes", "goToActivation", "uni", "url", "goToSignIn", "goToLogin", "showUserMenu", "closeUserMenu", "goToUserProfile", "title", "icon", "goToSettings", "goToAbout", "handleLogout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACoL;AACpL,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAurB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2G3sB;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MAEA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EAEAC;IAAA;IACAC;IACA;IACAC;MACA;IACA;EACA;EAEAC;IAAA;IACAF;IACA;IACAC;MACA;IACA;EACA;EAEAE;IACA;IACAC;MACAJ;MACA;MACA;MACA;MAEAA;MACAA;MACAA;MAEA;QACAA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;;gBAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAF;cAAA;gBAAAG;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAD;kBACAE;kBACAC;kBACAC;gBACA;;gBAEA;gBACA;kBACAzB;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;;gBAEA;gBAAA;gBAAA;gBAAA,OAEAoB;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAd;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAMAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAe;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAF;QACAC;MACA;IACA;IAEA;IACAE;MACAH;QACAC;MACA;IACA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAN;QACAO;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACAT;QACAO;QACAC;MACA;IACA;IAEA;IACAE;MACA;MACAV;QACAO;QACAC;MACA;IACA;IAEA;IACAG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClTA;AAAA;AAAA;AAAA;AAAogC,CAAgB,89BAAG,EAAC,C;;;;;;;;;;;ACAxhC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.userDisplayName.charAt(0)\n  var g1 = _vm.showUserMenuModal ? _vm.userDisplayName.charAt(0) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<!-- 页面头部 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"header-content\">\n\t\t\t\t<view class=\"header-info\">\n\t\t\t\t\t<view class=\"header-title\">小说下载器</view>\n\t\t\t\t\t<view class=\"header-subtitle\">激活码管理与每日签到</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"header-user\" @click=\"showUserMenu\">\n\t\t\t\t\t<view class=\"user-avatar\">{{ userDisplayName.charAt(0) }}</view>\n\t\t\t\t\t<view class=\"user-name\">{{ userDisplayName }}</view>\n\t\t\t\t\t<view class=\"user-arrow\">▼</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 功能导航卡片 -->\n\t\t<view class=\"nav-cards\">\n\t\t\t<!-- 激活码管理卡片 -->\n\t\t\t<view class=\"nav-card activation-card fade-in\" @click=\"goToActivation\">\n\t\t\t\t<view class=\"nav-card-icon\">🔑</view>\n\t\t\t\t<view class=\"nav-card-title\">激活码管理</view>\n\t\t\t\t<view class=\"nav-card-desc\">获取激活码、验证激活码、查询额度</view>\n\t\t\t\t<view class=\"nav-card-arrow\">→</view>\n\t\t\t</view>\n\n\t\t\t<!-- 每日签到卡片 -->\n\t\t\t<view class=\"nav-card signin-card fade-in\" style=\"animation-delay: 0.1s;\" @click=\"goToSignIn\">\n\t\t\t\t<view class=\"nav-card-icon\">📅</view>\n\t\t\t\t<view class=\"nav-card-title\">每日签到</view>\n\t\t\t\t<view class=\"nav-card-desc\">每日签到获取额度、查看签到历史</view>\n\t\t\t\t<view class=\"nav-card-arrow\">→</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 快速状态显示 -->\n\t\t<view v-if=\"userInfo\" class=\"quick-status fade-in\" style=\"animation-delay: 0.2s;\">\n\t\t\t<view class=\"status-header\">\n\t\t\t\t<view class=\"status-title\">快速状态</view>\n\t\t\t</view>\n\t\t\t<view class=\"status-content\">\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<view class=\"status-label\">激活码状态:</view>\n\t\t\t\t\t<view class=\"status-value success\">已验证</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<view class=\"status-label\">今日签到:</view>\n\t\t\t\t\t<view class=\"status-value\" :class=\"hasSigned ? 'success' : 'warning'\">\n\t\t\t\t\t\t{{ hasSigned ? '已签到' : '未签到' }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<view class=\"status-label\">剩余额度:</view>\n\t\t\t\t\t<view class=\"status-value\">{{ remainingTotalQuota || 0 }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 未验证激活码提示 -->\n\t\t<view v-else class=\"no-user-tip fade-in\" style=\"animation-delay: 0.2s;\">\n\t\t\t<view class=\"tip-icon\">⚠️</view>\n\t\t\t<view class=\"tip-text\">您还未验证激活码，请先前往激活码管理页面验证激活码</view>\n\t\t\t<view class=\"tip-buttons\">\n\t\t\t\t<button class=\"btn btn-primary\" @click=\"goToActivation\">立即验证</button>\n\t\t\t\t<button class=\"btn btn-secondary\" @click=\"goToLogin\">用户登录</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 用户菜单弹窗 -->\n\t\t<view v-if=\"showUserMenuModal\" class=\"modal-overlay\" @click=\"closeUserMenu\">\n\t\t\t<view class=\"user-menu-content\" @click.stop=\"\">\n\t\t\t\t<view class=\"user-menu-header\">\n\t\t\t\t\t<view class=\"menu-avatar\">{{ userDisplayName.charAt(0) }}</view>\n\t\t\t\t\t<view class=\"menu-user-info\">\n\t\t\t\t\t\t<view class=\"menu-username\">{{ userDisplayName }}</view>\n\t\t\t\t\t\t<view class=\"menu-user-id\">ID: {{ currentUser.userId || 'N/A' }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-menu-list\">\n\t\t\t\t\t<view class=\"menu-item\" @click=\"goToUserProfile\">\n\t\t\t\t\t\t<view class=\"menu-icon\">👤</view>\n\t\t\t\t\t\t<view class=\"menu-text\">个人信息</view>\n\t\t\t\t\t\t<view class=\"menu-arrow\">→</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"menu-item\" @click=\"goToSettings\">\n\t\t\t\t\t\t<view class=\"menu-icon\">⚙️</view>\n\t\t\t\t\t\t<view class=\"menu-text\">设置</view>\n\t\t\t\t\t\t<view class=\"menu-arrow\">→</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"menu-item\" @click=\"goToAbout\">\n\t\t\t\t\t\t<view class=\"menu-icon\">ℹ️</view>\n\t\t\t\t\t\t<view class=\"menu-text\">关于</view>\n\t\t\t\t\t\t<view class=\"menu-arrow\">→</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"menu-item logout-item\" @click=\"handleLogout\">\n\t\t\t\t\t\t<view class=\"menu-icon\">🚪</view>\n\t\t\t\t\t\t<view class=\"menu-text\">退出登录</view>\n\t\t\t\t\t\t<view class=\"menu-arrow\">→</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\tsignInApi,\n\t\ttokenApi,\n\t\tutils\n\t} from '@/api/index'\n\timport { checkLogin, getUserInfo, logout, getToken, isLoggedIn } from '@/utils/auth'\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 用户信息\n\t\t\t\tuserInfo: null,\n\t\t\t\tcurrentUser: null,\n\t\t\t\tuserQuota: {\n\t\t\t\t\tbaseLimit: 0,\n\t\t\t\t\tactivityLimit: 0,\n\t\t\t\t\ttempLimit: 0,\n\t\t\t\t\ttotalLimit: 0,\n\t\t\t\t\tcurrentUseNum: 0,\n\t\t\t\t\tremainingLimit: 0,\n\t\t\t\t\tpermanentQuota: 0\n\t\t\t\t},\n\n\t\t\t\t// 签到状态\n\t\t\t\thasSigned: false,\n\n\t\t\t\t// 弹窗状态\n\t\t\t\tshowUserMenuModal: false\n\t\t\t}\n\t\t},\n\n\t\tcomputed: {\n\t\t\tremainingTotalQuota() {\n\t\t\t\tconst totalQuota = (this.userQuota.totalLimit || 0) + (this.userQuota.permanentQuota || 0)\n\t\t\t\tconst used = this.userQuota.currentUseNum || 0\n\t\t\t\treturn Math.max(0, totalQuota - used)\n\t\t\t},\n\n\t\t\t// 用户显示名称\n\t\t\tuserDisplayName() {\n\t\t\t\tif (this.currentUser) {\n\t\t\t\t\treturn this.currentUser.username || this.currentUser.phone || '用户'\n\t\t\t\t}\n\t\t\t\treturn '未登录'\n\t\t\t}\n\t\t},\n\n\t\tonLoad() {\n\t\t\tconsole.log('首页 onLoad - 开始检查登录状态')\n\t\t\t// 延迟检查登录状态，确保token已经保存\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.checkLoginAndInit()\n\t\t\t}, 100)\n\t\t},\n\n\t\tonShow() {\n\t\t\tconsole.log('首页 onShow - 开始检查登录状态')\n\t\t\t// 延迟检查登录状态\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.checkLoginAndInit()\n\t\t\t}, 100)\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 检查登录状态并初始化页面\n\t\t\tcheckLoginAndInit() {\n\t\t\t\tconsole.log('检查登录状态...')\n\t\t\t\tconst token = getToken()\n\t\t\t\tconst userInfo = getUserInfo()\n\t\t\t\tconst loginStatus = isLoggedIn()\n\n\t\t\t\tconsole.log('Token:', token)\n\t\t\t\tconsole.log('UserInfo:', userInfo)\n\t\t\t\tconsole.log('登录状态:', loginStatus)\n\n\t\t\t\tif (!loginStatus) {\n\t\t\t\t\tconsole.log('用户未登录，跳转到登录页')\n\t\t\t\t\tif (!checkLogin(false)) {\n\t\t\t\t\t\treturn\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log('用户已登录，初始化页面')\n\t\t\t\t\tthis.initPage()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 初始化页面\n\t\t\tasync initPage() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取当前登录用户信息\n\t\t\t\t\tthis.currentUser = getUserInfo()\n\n\t\t\t\t\t// 获取用户token状态\n\t\t\t\t\tconst tokenRes = await tokenApi.getUserToken()\n\t\t\t\t\tif (tokenRes.code === 200 && tokenRes.data) {\n\t\t\t\t\t\tconst tokenName = tokenRes.data.tokenName\n\t\t\t\t\t\tif (tokenName) {\n\t\t\t\t\t\t\t// 验证激活码获取用户信息\n\t\t\t\t\t\t\tconst res = await tokenApi.getQuota(tokenName)\n\t\t\t\t\t\t\tif (res.code === 200) {\n\t\t\t\t\t\t\t\tthis.userInfo = {\n\t\t\t\t\t\t\t\t\ttokenName: tokenName,\n\t\t\t\t\t\t\t\t\ttokenTime: utils.formatDate(new Date(), 'YYYY-MM-DD'),\n\t\t\t\t\t\t\t\t\ttokenStatus: 0,\n\t\t\t\t\t\t\t\t\ttokenCompCode: '尚未绑定'\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// 保存额度信息\n\t\t\t\t\t\t\t\tthis.userQuota = res.data || {\n\t\t\t\t\t\t\t\t\tbaseLimit: 0,\n\t\t\t\t\t\t\t\t\tactivityLimit: 0,\n\t\t\t\t\t\t\t\t\ttempLimit: 0,\n\t\t\t\t\t\t\t\t\ttotalLimit: 0,\n\t\t\t\t\t\t\t\t\tcurrentUseNum: 0,\n\t\t\t\t\t\t\t\t\tremainingLimit: 0,\n\t\t\t\t\t\t\t\t\tpermanentQuota: 0\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// 检查今日是否已签到\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tconst signRes = await signInApi.checkSignInToday(tokenName)\n\t\t\t\t\t\t\t\t\tif (signRes.code === 200) {\n\t\t\t\t\t\t\t\t\t\tthis.hasSigned = signRes.data\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\t\t\tconsole.error('检查签到状态失败:', error)\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取用户信息失败:', error)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 前往激活码管理页面\n\t\t\tgoToActivation() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/activation/activation'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 前往签到页面\n\t\t\tgoToSignIn() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/signin/signin'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 前往登录页面\n\t\t\tgoToLogin() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 显示用户菜单\n\t\t\tshowUserMenu() {\n\t\t\t\tthis.showUserMenuModal = true\n\t\t\t},\n\n\t\t\t// 关闭用户菜单\n\t\t\tcloseUserMenu() {\n\t\t\t\tthis.showUserMenuModal = false\n\t\t\t},\n\n\t\t\t// 前往个人信息页面\n\t\t\tgoToUserProfile() {\n\t\t\t\tthis.closeUserMenu()\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 前往设置页面\n\t\t\tgoToSettings() {\n\t\t\t\tthis.closeUserMenu()\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 前往关于页面\n\t\t\tgoToAbout() {\n\t\t\t\tthis.closeUserMenu()\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 处理退出登录\n\t\t\tasync handleLogout() {\n\t\t\t\tthis.closeUserMenu()\n\t\t\t\tawait logout()\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t/* 页面容器 */\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tpadding: 20rpx;\n\t}\n\n\t/* 页面头部 */\n\t.header {\n\t\tpadding: 40rpx 0;\n\t\tcolor: #ffffff;\n\t}\n\n\t.header-content {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t}\n\n\t.header-info {\n\t\tflex: 1;\n\t}\n\n\t.header-title {\n\t\tfont-size: 56rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 15rpx;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n\t}\n\n\t.header-subtitle {\n\t\tfont-size: 32rpx;\n\t\topacity: 0.9;\n\t}\n\n\t/* 用户信息 */\n\t.header-user {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground: rgba(255, 255, 255, 0.1);\n\t\tbackdrop-filter: blur(10px);\n\t\tborder-radius: 50rpx;\n\t\tpadding: 15rpx 25rpx;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.header-user:hover {\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\ttransform: translateY(-2rpx);\n\t}\n\n\t.user-avatar {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #ffffff;\n\t\tmargin-right: 15rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.user-name {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tmargin-right: 10rpx;\n\t\tmax-width: 120rpx;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t\twhite-space: nowrap;\n\t}\n\n\t.user-arrow {\n\t\tfont-size: 20rpx;\n\t\topacity: 0.8;\n\t\ttransition: transform 0.3s ease;\n\t}\n\n\t.header-user:hover .user-arrow {\n\t\ttransform: rotate(180deg);\n\t}\n\n\t/* 导航卡片容器 */\n\t.nav-cards {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 30rpx;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t/* 导航卡片 */\n\t.nav-card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx 30rpx;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.3s ease;\n\t\tcursor: pointer;\n\t\tposition: relative;\n\t\toverflow: hidden;\n\t}\n\n\t.nav-card:hover {\n\t\ttransform: translateY(-6rpx);\n\t\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);\n\t}\n\n\t.nav-card::before {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 6rpx;\n\t\tbackground: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n\t}\n\n\t.activation-card::before {\n\t\tbackground: linear-gradient(90deg, #f093fb 0%, #f5576c 100%);\n\t}\n\n\t.signin-card::before {\n\t\tbackground: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);\n\t}\n\n\t.nav-card-icon {\n\t\tfont-size: 60rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.nav-card-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t\tmargin-bottom: 15rpx;\n\t}\n\n\t.nav-card-desc {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tline-height: 1.5;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.nav-card-arrow {\n\t\tposition: absolute;\n\t\tright: 30rpx;\n\t\ttop: 50%;\n\t\ttransform: translateY(-50%);\n\t\tfont-size: 40rpx;\n\t\tcolor: #409EFF;\n\t\tfont-weight: bold;\n\t}\n\n\t/* 快速状态 */\n\t.quick-status {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\t\tmargin-bottom: 30rpx;\n\t\toverflow: hidden;\n\t}\n\n\t.status-header {\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t\tbackground: linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%);\n\t}\n\n\t.status-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.status-content {\n\t\tpadding: 30rpx;\n\t}\n\n\t.status-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding: 15rpx 0;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t}\n\n\t.status-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.status-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t}\n\n\t.status-value {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.status-value.success {\n\t\tcolor: #67C23A;\n\t}\n\n\t.status-value.warning {\n\t\tcolor: #E6A23C;\n\t}\n\n\t/* 未验证提示 */\n\t.no-user-tip {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 50rpx 30rpx;\n\t\ttext-align: center;\n\t\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.tip-icon {\n\t\tfont-size: 80rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.tip-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666666;\n\t\tmargin-bottom: 40rpx;\n\t\tline-height: 1.6;\n\t}\n\n\t.tip-buttons {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t\tjustify-content: center;\n\t}\n\n\t.tip-buttons .btn {\n\t\tflex: 1;\n\t\tmax-width: 200rpx;\n\t}\n\n\t/* 按钮样式 */\n\t.btn {\n\t\tpadding: 25rpx 50rpx;\n\t\tborder-radius: 30rpx;\n\t\tborder: none;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\ttext-align: center;\n\t\tdisplay: inline-block;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.btn:hover {\n\t\ttransform: translateY(-2rpx);\n\t\tbox-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);\n\t}\n\n\t.btn-primary {\n\t\tbackground: linear-gradient(135deg, #409EFF 0%, #66b1ff 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.btn-secondary {\n\t\tbackground: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t/* 动画效果 */\n\t.fade-in {\n\t\tanimation: fadeInUp 0.6s ease-out;\n\t}\n\n\t@keyframes fadeInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(30rpx);\n\t\t}\n\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t/* 用户菜单弹窗 */\n\t.modal-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tz-index: 1000;\n\t\tanimation: fadeIn 0.3s ease-out;\n\t}\n\n\t@keyframes fadeIn {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t}\n\t}\n\n\t.user-menu-content {\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\twidth: 90%;\n\t\tmax-width: 500rpx;\n\t\tbox-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);\n\t\tanimation: slideInUp 0.3s ease-out;\n\t\toverflow: hidden;\n\t}\n\n\t@keyframes slideInUp {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(50rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.user-menu-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 40rpx 30rpx;\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tcolor: #ffffff;\n\t}\n\n\t.menu-avatar {\n\t\twidth: 80rpx;\n\t\theight: 80rpx;\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tmargin-right: 20rpx;\n\t\tborder: 3rpx solid rgba(255, 255, 255, 0.3);\n\t}\n\n\t.menu-user-info {\n\t\tflex: 1;\n\t}\n\n\t.menu-username {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.menu-user-id {\n\t\tfont-size: 24rpx;\n\t\topacity: 0.8;\n\t}\n\n\t.user-menu-list {\n\t\tpadding: 20rpx 0;\n\t}\n\n\t.menu-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 25rpx 30rpx;\n\t\tcursor: pointer;\n\t\ttransition: all 0.3s ease;\n\t\tborder-bottom: 1rpx solid #f5f5f5;\n\t}\n\n\t.menu-item:hover {\n\t\tbackground-color: #f8f9fa;\n\t}\n\n\t.menu-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\n\t.menu-item.logout-item {\n\t\tcolor: #f56c6c;\n\t}\n\n\t.menu-item.logout-item:hover {\n\t\tbackground-color: #fef0f0;\n\t}\n\n\t.menu-icon {\n\t\tfont-size: 32rpx;\n\t\tmargin-right: 20rpx;\n\t\twidth: 40rpx;\n\t\ttext-align: center;\n\t}\n\n\t.menu-text {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: 500;\n\t}\n\n\t.menu-arrow {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999999;\n\t\ttransition: transform 0.3s ease;\n\t}\n\n\t.menu-item:hover .menu-arrow {\n\t\ttransform: translateX(5rpx);\n\t}\n</style>\n", "import mod from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755076265640\n      var cssReload = require(\"D:/app/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}